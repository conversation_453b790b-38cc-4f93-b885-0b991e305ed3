import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';

// Ng-Zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzMessageService } from 'ng-zorro-antd/message';

interface ServiceConfig {
  serviceId: string;
  serviceName: string;
  [eventType: string]: boolean | string;
}

@Component({
  selector: 'app-services-config',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzTableModule,
    NzSwitchModule,
    NzButtonModule,
    NzIconModule,
    NzToolTipModule
  ],
  templateUrl: './services-config.component.html',
  styleUrl: './services-config.component.css'
})
export class ServicesConfigComponent implements OnInit {
  category: string = '';
  categoryTitle: string = '';
  eventTypes: string[] = [];
  services: ServiceConfig[] = [];
  isLoading = false;

  // Services disponibles
  allServices = [
    'DJ',
    'Décoration florale',
    'Décoration lumière',
    'Orchestre célèbre',
    'Orchestre pas célèbre',
    'Photographe',
    'Vidéaste',
    'Animation enfants',
    'Magicien',
    'Traiteur externe',
    'Service de nettoyage',
    'Sécurité',
    'Valet parking',
    'Location de voitures',
    'Fleuriste',
    'Pâtissier',
    'Sonorisation',
    'Éclairage scénique'
  ];

  constructor(
    private route: ActivatedRoute,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.category = params['category'] || 'particulier'; // Valeur par défaut
      console.log('Category from route:', this.category); // Debug
      this.initializeCategory();
      this.loadServicesConfig();
    });
  }

  initializeCategory(): void {
    switch (this.category) {
      case 'particulier':
        this.categoryTitle = 'Événements Particulier';
        this.eventTypes = [
          'Mariage', 'Fiançailles', 'Aqiqah', 'Repas livré',
          'Soutenance', 'Funérailles', 'Anniversaire', 'Buffet',
          'Brunch', 'Ftour', 'Cocktails dînatoire', 'Cocktails'
        ];
        break;
      case 'entreprise':
        this.categoryTitle = 'Événements Entreprise';
        this.eventTypes = [
          'Manifestation ou fête de travail', 'Buffet', 'Repas séminaire',
          'Cocktails', 'Cocktails dînatoire', 'Pause café', 'Lunch box', 'Brunch'
        ];
        break;
      case 'evenements-speciaux':
        this.categoryTitle = 'Événements Spéciaux';
        this.eventTypes = ['Cinéma', 'Cérémonie'];
        break;
      case 'location-materiel':
        this.categoryTitle = 'Location de Matériel';
        this.eventTypes = ['Chapiteaux', 'Estrade', 'Dalo', 'Scène', 'Salle de fête'];
        break;
      default:
        console.warn('Category not recognized:', this.category);
        this.categoryTitle = 'Événements Particulier';
        this.eventTypes = [
          'Mariage', 'Fiançailles', 'Aqiqah', 'Repas livré',
          'Soutenance', 'Funérailles', 'Anniversaire', 'Buffet',
          'Brunch', 'Ftour', 'Cocktails dînatoire', 'Cocktails'
        ];
        break;
    }
  }

  loadServicesConfig(): void {
    this.isLoading = true;

    // Simulation de chargement des données
    setTimeout(() => {
      this.services = this.allServices.map(service => {
        const config: ServiceConfig = {
          serviceId: service.toLowerCase().replace(/\s+/g, '-'),
          serviceName: service
        };

        // Initialiser avec des valeurs par défaut
        this.eventTypes.forEach(eventType => {
          config[eventType] = Math.random() > 0.5; // Valeurs aléatoires pour la démo
        });

        return config;
      });

      this.isLoading = false;
    }, 500);
  }

  onServiceToggle(service: ServiceConfig, eventType: string, enabled: boolean): void {
    service[eventType] = enabled;
    console.log(`Service ${service.serviceName} pour ${eventType}: ${enabled}`);
  }

  saveConfiguration(): void {
    this.isLoading = true;

    // Simulation de sauvegarde
    setTimeout(() => {
      this.message.success('Configuration sauvegardée avec succès !');
      this.isLoading = false;
      console.log('Configuration sauvegardée:', this.services);
    }, 1000);
  }

  resetConfiguration(): void {
    this.loadServicesConfig();
    this.message.info('Configuration réinitialisée');
  }



  get activeConfigurationsCount(): number {
    return this.services.reduce((count, service) =>
      count + this.eventTypes.filter(type => service[type]).length, 0);
  }

  addNewService(): void {
    const serviceName = prompt('Nom du nouveau service :');
    if (serviceName && serviceName.trim()) {
      const newService: ServiceConfig = {
        serviceId: serviceName.toLowerCase().replace(/\s+/g, '-'),
        serviceName: serviceName.trim()
      };

      // Initialiser avec false pour tous les types d'événements
      this.eventTypes.forEach(eventType => {
        newService[eventType] = false;
      });

      this.services.push(newService);
      this.message.success(`Service "${serviceName}" ajouté avec succès !`);
    }
  }

  removeService(serviceIndex: number): void {
    if (this.services.length <= 1) {
      this.message.warning('Impossible de supprimer le dernier service !');
      return;
    }

    const service = this.services[serviceIndex];
    if (confirm(`Êtes-vous sûr de vouloir supprimer le service "${service.serviceName}" ?`)) {
      this.services.splice(serviceIndex, 1);
      this.message.success(`Service "${service.serviceName}" supprimé !`);
    }
  }
}
