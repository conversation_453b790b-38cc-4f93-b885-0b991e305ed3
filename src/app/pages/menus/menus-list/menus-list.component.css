/* Container principal */
.menus-container {
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
  min-height: 100vh;
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.add-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 12px !important;
  height: 44px !important;
  padding: 0 24px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(139, 21, 56, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.add-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(139, 21, 56, 0.4) !important;
}

/* Section de recherche */
.search-section {
  margin-bottom: 32px;
}

.search-card {
  border-radius: 16px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid var(--apple-gray-200) !important;
}

.search-row {
  display: flex;
  justify-content: center;
}

.search-input-group {
  width: 100%;
  max-width: 500px;
}

.search-input {
  height: 44px !important;
  border-radius: 12px !important;
  border: 2px solid var(--apple-gray-200) !important;
  transition: all 0.3s ease !important;
  font-size: 16px !important;
}

.search-input:focus {
  border-color: var(--apple-grenat) !important;
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1) !important;
}

.clear-search-btn {
  color: var(--apple-gray-400) !important;
  transition: all 0.2s ease !important;
}

.clear-search-btn:hover {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

/* Grille des menus */
.menus-grid {
  margin-bottom: 32px;
}

/* Cartes de menu Apple-like */
.menu-card {
  border-radius: 20px !important;
  border: 1px solid var(--apple-gray-200) !important;
  background: var(--apple-white) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  overflow: hidden !important;
  height: 100% !important;
}

.menu-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 12px 40px rgba(139, 21, 56, 0.15) !important;
  border-color: var(--apple-grenat-pale) !important;
}

:host ::ng-deep .menu-card .ant-card-body {
  padding: 24px !important;
  height: calc(100% - 60px) !important;
  display: flex !important;
  flex-direction: column !important;
}

:host ::ng-deep .menu-card .ant-card-actions {
  background: var(--apple-gray-50) !important;
  border-top: 1px solid var(--apple-gray-200) !important;
  padding: 12px 24px !important;
}

:host ::ng-deep .menu-card .ant-card-actions > li {
  margin: 0 8px !important;
}

:host ::ng-deep .menu-card .ant-card-actions > li > span {
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .menu-card .ant-card-actions > li > span:hover {
  background: var(--apple-grenat-pale) !important;
}

/* Header du menu */
.menu-header {
  margin-bottom: 20px;
  border-bottom: 1px solid var(--apple-gray-100);
  padding-bottom: 16px;
}

.menu-title {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--apple-grenat);
  line-height: 1.3;
}

.menu-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.menu-price {
  font-size: 18px;
  font-weight: 700;
  color: var(--apple-grenat);
  background: var(--apple-grenat-soft);
  padding: 4px 12px;
  border-radius: 12px;
}

.menu-description {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
  line-height: 1.4;
  font-style: italic;
}

/* Liste des éléments du menu */
.menu-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.menu-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--apple-gray-50);
  transition: all 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background: var(--apple-grenat-soft);
  margin: 0 -12px;
  padding: 8px 12px;
  border-radius: 8px;
}

.check-icon {
  color: var(--apple-grenat);
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.check-icon.checked {
  color: var(--apple-grenat);
}

.item-text {
  font-size: 14px;
  color: var(--apple-gray-700);
  line-height: 1.4;
  flex: 1;
}

/* État vide */
.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.empty-card {
  border-radius: 20px !important;
  border: 2px dashed var(--apple-gray-300) !important;
  background: var(--apple-white) !important;
  max-width: 400px !important;
  width: 100% !important;
}

.empty-content {
  text-align: center;
  padding: 32px 24px;
}

.empty-icon {
  font-size: 64px;
  color: var(--apple-gray-400);
  margin-bottom: 16px;
}

.empty-content h3 {
  margin: 16px 0 8px 0;
  color: var(--apple-gray-700);
  font-weight: 600;
}

.empty-content p {
  margin-bottom: 24px;
  color: var(--apple-gray-500);
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .menus-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .page-title {
    font-size: 24px;
    text-align: center;
  }
  
  .search-input-group {
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  .menu-card:hover {
    transform: none !important;
  }
  
  .menu-title {
    font-size: 18px;
  }
  
  .menu-price {
    font-size: 16px;
  }
}
