import { Routes } from '@angular/router';

export const EVENTS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./events-list/events-list.component').then(m => m.EventsListComponent),
    data: { breadcrumb: { label: 'Liste des événements' } }
  },
  {
    path: 'new',
    loadComponent: () => import('../../components/event-form/event-form.component').then(m => m.EventFormComponent),
    data: { breadcrumb: { label: 'Nouvel événement', icon: 'plus' } }
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('../../components/event-form/event-form.component').then(m => m.EventFormComponent),
    data: { breadcrumb: { label: 'Modifier événement', icon: 'edit' } }
  }
];
