.inventory-container {
  padding: 24px 32px 16px 32px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.add-btn {
  border-radius: 8px;
  border: 2px dashed var(--apple-gray-300);
  color: var(--apple-grenat);
  transition: all 0.2s ease;
}

.add-btn:hover {
  border-color: var(--apple-grenat);
  background: var(--apple-grenat-soft);
}

.save-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3) !important;
}

/* Statistiques */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(139, 21, 56, 0.2);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  min-height: 100px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-icon svg {
  width: 28px;
  height: 28px;
}

.types-icon {
  background: linear-gradient(135deg, rgba(139, 21, 56, 0.1), rgba(169, 29, 66, 0.1));
  color: #8B1538;
}

.total-icon {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 209, 88, 0.1));
  color: #34c759;
}

.categories-icon {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(0, 86, 204, 0.1));
  color: #007aff;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--apple-grenat);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--apple-gray-600);
  margin-top: 4px;
}

/* Grille des matériaux */
.materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.material-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  padding: 24px;
}

.material-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(139, 21, 56, 0.2);
}

/* Header du matériel */
.material-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.material-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.material-icon svg {
  width: 24px;
  height: 24px;
}

.material-icon.icon-1 {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 209, 88, 0.1));
  color: #34c759;
}

.material-icon.icon-2 {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 140, 0, 0.1));
  color: #ff9500;
}

.material-icon.icon-3 {
  background: linear-gradient(135deg, rgba(175, 82, 222, 0.1), rgba(191, 90, 242, 0.1));
  color: #af52de;
}

.material-icon.icon-4 {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(0, 86, 204, 0.1));
  color: #007aff;
}

.material-icon.icon-5 {
  background: linear-gradient(135deg, rgba(139, 21, 56, 0.1), rgba(169, 29, 66, 0.1));
  color: #8B1538;
}

.material-icon.icon-6 {
  background: linear-gradient(135deg, rgba(50, 215, 75, 0.1), rgba(48, 209, 88, 0.1));
  color: #32d74b;
}

.material-icon.icon-7 {
  background: linear-gradient(135deg, rgba(48, 209, 88, 0.1), rgba(52, 199, 89, 0.1));
  color: #30d158;
}

.material-icon.icon-8 {
  background: linear-gradient(135deg, rgba(255, 204, 2, 0.1), rgba(255, 214, 10, 0.1));
  color: #ffcc02;
}

.material-info {
  flex: 1;
  min-width: 0;
}

.material-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-gray-900);
  line-height: 1.2;
}

.material-description {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--apple-gray-600);
  line-height: 1.4;
}

.material-category {
  display: inline-block;
  padding: 4px 8px;
  background: var(--apple-gray-100);
  color: var(--apple-gray-700);
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.material-actions {
  flex-shrink: 0;
}

.delete-btn {
  color: var(--apple-gray-400) !important;
  transition: all 0.2s ease !important;
}

.delete-btn:hover {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.1) !important;
}

/* Section quantité */
.quantity-section {
  margin-bottom: 16px;
}

.quantity-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: var(--apple-gray-700);
}

.unit-label {
  font-size: 12px;
  color: var(--apple-gray-500);
  font-weight: 400;
}

.quantity-controls {
  display: flex;
  justify-content: center;
}

.quantity-input {
  width: 120px;
}

:host ::ng-deep .quantity-input .ant-input-number {
  border-radius: 8px;
  border: 2px solid var(--apple-gray-200);
  transition: all 0.2s ease;
}

:host ::ng-deep .quantity-input .ant-input-number:focus,
:host ::ng-deep .quantity-input .ant-input-number-focused {
  border-color: var(--apple-grenat);
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1);
}

/* Indicateur de disponibilité */
.availability-indicator {
  display: flex;
  justify-content: center;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.available {
  background: rgba(52, 199, 89, 0.1);
  color: #34c759;
  border: 1px solid rgba(52, 199, 89, 0.2);
}

.status-badge.low-stock {
  background: rgba(255, 149, 0, 0.1);
  color: #ff9500;
  border: 1px solid rgba(255, 149, 0, 0.2);
}

.status-badge.unavailable {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  border: 1px solid rgba(255, 59, 48, 0.2);
}

/* État vide */
.empty-state {
  border-radius: 20px;
  border: 2px dashed rgba(139, 21, 56, 0.2);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  padding: 24px;
}

.empty-content {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 16px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--apple-gray-400);
}

.empty-icon svg {
  width: 64px;
  height: 64px;
}

.empty-content h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--apple-gray-800);
}

.empty-content p {
  margin: 0 0 24px 0;
  color: var(--apple-gray-600);
}

.add-first-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
}

/* Responsive */
@media (max-width: 768px) {
  .inventory-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .materials-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
  }
}
