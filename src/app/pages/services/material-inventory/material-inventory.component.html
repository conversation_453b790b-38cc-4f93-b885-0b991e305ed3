<div class="inventory-container">
  <!-- Header avec titre et actions -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">Inventaire du matériel de location</h2>
      <div class="header-actions">
        <button 
          nz-button 
          nzType="default" 
          (click)="resetInventory()"
          [nzLoading]="isLoading"
          nz-tooltip="Réinitialiser l'inventaire">
          <nz-icon nzType="reload"></nz-icon>
          Réinitialiser
        </button>
        <button 
          nz-button 
          nzType="dashed" 
          (click)="addNewMaterial()"
          class="add-btn">
          <nz-icon nzType="plus"></nz-icon>
          Ajouter matériel
        </button>
        <button 
          nz-button 
          nzType="primary" 
          (click)="saveInventory()"
          [nzLoading]="isLoading"
          class="save-btn">
          <nz-icon nzType="save"></nz-icon>
          <PERSON>uvegarder
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section">
    <nz-card class="stat-card">
      <div class="stat-content">
        <div class="stat-icon">📦</div>
        <div class="stat-info">
          <div class="stat-value">{{ materials.length }}</div>
          <div class="stat-label">Types de matériel</div>
        </div>
      </div>
    </nz-card>
    
    <nz-card class="stat-card">
      <div class="stat-content">
        <div class="stat-icon">🔢</div>
        <div class="stat-info">
          <div class="stat-value">{{ getTotalItems() }}</div>
          <div class="stat-label">Articles au total</div>
        </div>
      </div>
    </nz-card>
    
    <nz-card class="stat-card">
      <div class="stat-content">
        <div class="stat-icon">🏷️</div>
        <div class="stat-info">
          <div class="stat-value">{{ getCategoriesCount() }}</div>
          <div class="stat-label">Catégories</div>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Grille des matériaux -->
  <div class="materials-grid">
    @for (material of materials; track material.id) {
      <nz-card class="material-card" [nzLoading]="isLoading">
        <div class="material-header">
          <div class="material-icon" [style.background-color]="material.color">
            {{ material.icon }}
          </div>
          <div class="material-info">
            <h3 class="material-name">{{ material.name }}</h3>
            <p class="material-description">{{ material.description }}</p>
            <span class="material-category">{{ material.category }}</span>
          </div>
          <div class="material-actions">
            <button 
              nz-button 
              nzType="text" 
              nzDanger
              nzSize="small"
              (click)="deleteMaterial(material)"
              nz-tooltip="Supprimer ce matériel"
              class="delete-btn">
              <nz-icon nzType="delete"></nz-icon>
            </button>
          </div>
        </div>
        
        <div class="quantity-section">
          <div class="quantity-label">
            <span>Quantité disponible</span>
            <span class="unit-label">{{ material.unit }}</span>
          </div>
          <div class="quantity-controls">
            <nz-input-number
              [(ngModel)]="material.quantity"
              [nzMin]="0"
              [nzMax]="9999"
              [nzStep]="1"
              (ngModelChange)="updateQuantity(material, $event)"
              class="quantity-input">
            </nz-input-number>
          </div>
        </div>
        
        <div class="availability-indicator">
          @if (material.quantity === 0) {
            <div class="status-badge unavailable">
              <nz-icon nzType="close-circle"></nz-icon>
              Non disponible
            </div>
          } @else if (material.quantity <= 2) {
            <div class="status-badge low-stock">
              <nz-icon nzType="exclamation-circle"></nz-icon>
              Stock faible
            </div>
          } @else {
            <div class="status-badge available">
              <nz-icon nzType="check-circle"></nz-icon>
              Disponible
            </div>
          }
        </div>
      </nz-card>
    }
  </div>

  <!-- Message si aucun matériel -->
  @if (materials.length === 0) {
    <nz-card class="empty-state">
      <div class="empty-content">
        <div class="empty-icon">📦</div>
        <h3>Aucun matériel dans l'inventaire</h3>
        <p>Commencez par ajouter du matériel de location à votre inventaire.</p>
        <button 
          nz-button 
          nzType="primary" 
          (click)="addNewMaterial()"
          class="add-first-btn">
          <nz-icon nzType="plus"></nz-icon>
          Ajouter le premier matériel
        </button>
      </div>
    </nz-card>
  }
</div>
