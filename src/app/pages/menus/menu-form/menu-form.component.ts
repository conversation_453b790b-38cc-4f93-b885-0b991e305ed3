import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';

@Component({
  selector: 'app-menu-form',
  standalone: true,
  imports: [CommonModule, NzCardModule],
  template: `
    <div class="menu-form-container">
      <nz-card nzTitle="Formulaire de menu">
        <p>Formulaire de création/modification de menu à implémenter</p>
      </nz-card>
    </div>
  `,
  styles: [`
    .menu-form-container {
      padding: 24px 32px 16px 32px;
      max-width: 1000px;
      margin: 0 auto;
    }
  `]
})
export class MenuFormComponent {}
