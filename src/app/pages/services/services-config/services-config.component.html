<div class="services-config-container">
  <!-- Header avec titre -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">Configuration des services - {{ categoryTitle }}</h2>
      <div class="header-actions">
        <button 
          nz-button 
          nzType="default" 
          (click)="resetConfiguration()"
          [nzLoading]="isLoading">
          <nz-icon nzType="reload"></nz-icon>
          Réinitialiser
        </button>
        <button 
          nz-button 
          nzType="primary" 
          (click)="saveConfiguration()"
          [nzLoading]="isLoading"
          class="save-btn">
          <nz-icon nzType="save"></nz-icon>
          Sauvegarder
        </button>
      </div>
    </div>
  </div>

  <nz-card class="config-card">
    <div class="config-description">
      <p>
        Configurez quels services seront disponibles pour chaque type d'événement dans la catégorie 
        <strong>{{ categoryTitle }}</strong>. 
        Activez ou désactivez les services en utilisant les interrupteurs dans le tableau ci-dessous.
      </p>
    </div>

    <!-- Tableau de configuration -->
    <div class="config-table-container">
      <nz-table 
        [nzData]="services" 
        [nzLoading]="isLoading"
        nzBordered
        nzSize="middle"
        [nzScroll]="{ x: '100%' }"
        class="config-table">
        
        <thead>
          <tr>
            <th nzWidth="200px" class="service-header">
              <div class="header-content">
                <span>Services</span>
                <div class="header-actions">
                  <button 
                    nz-button 
                    nzType="text" 
                    nzSize="small"
                    nz-tooltip="Activer tous les services"
                    (click)="services.forEach(s => enableAllForService(s))">
                    <nz-icon nzType="check-circle"></nz-icon>
                  </button>
                  <button 
                    nz-button 
                    nzType="text" 
                    nzSize="small"
                    nz-tooltip="Désactiver tous les services"
                    (click)="services.forEach(s => disableAllForService(s))">
                    <nz-icon nzType="close-circle"></nz-icon>
                  </button>
                </div>
              </div>
            </th>
            @for (eventType of eventTypes; track eventType) {
              <th class="event-type-header">
                <div class="header-content">
                  <span class="event-type-name">{{ eventType }}</span>
                  <div class="header-actions">
                    <button 
                      nz-button 
                      nzType="text" 
                      nzSize="small"
                      nz-tooltip="Activer pour tous les services"
                      (click)="enableAllForEventType(eventType)">
                      <nz-icon nzType="check-circle"></nz-icon>
                    </button>
                    <button 
                      nz-button 
                      nzType="text" 
                      nzSize="small"
                      nz-tooltip="Désactiver pour tous les services"
                      (click)="disableAllForEventType(eventType)">
                      <nz-icon nzType="close-circle"></nz-icon>
                    </button>
                  </div>
                </div>
              </th>
            }
          </tr>
        </thead>
        
        <tbody>
          @for (service of services; track service.serviceId) {
            <tr>
              <td class="service-name">
                <div class="service-info">
                  <span class="service-title">{{ service.serviceName }}</span>
                  <div class="service-actions">
                    <button 
                      nz-button 
                      nzType="text" 
                      nzSize="small"
                      nz-tooltip="Activer pour tous les événements"
                      (click)="enableAllForService(service)">
                      <nz-icon nzType="check-circle"></nz-icon>
                    </button>
                    <button 
                      nz-button 
                      nzType="text" 
                      nzSize="small"
                      nz-tooltip="Désactiver pour tous les événements"
                      (click)="disableAllForService(service)">
                      <nz-icon nzType="close-circle"></nz-icon>
                    </button>
                  </div>
                </div>
              </td>
              @for (eventType of eventTypes; track eventType) {
                <td class="switch-cell">
                  <nz-switch 
                    [ngModel]="service[eventType]"
                    (ngModelChange)="onServiceToggle(service, eventType, $event)"
                    nzSize="small">
                  </nz-switch>
                </td>
              }
            </tr>
          }
        </tbody>
      </nz-table>
    </div>

    <!-- Résumé -->
    <div class="config-summary">
      <div class="summary-stats">
        <div class="stat-item">
          <span class="stat-label">Services configurés :</span>
          <span class="stat-value">{{ services.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Types d'événements :</span>
          <span class="stat-value">{{ eventTypes.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Configurations actives :</span>
          <span class="stat-value">
            {{ services.reduce((count, service) => 
                count + eventTypes.filter(type => service[type]).length, 0) }}
          </span>
        </div>
      </div>
    </div>

  </nz-card>
</div>
