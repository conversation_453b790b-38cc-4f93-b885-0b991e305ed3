import { Component, OnInit } from '@angular/core';
import {RouterLink, RouterLinkActive, RouterOutlet, Router, NavigationEnd, ActivatedRoute} from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { filter } from 'rxjs/operators';

interface BreadcrumbItem {
  label: string;
  url?: string;
  icon?: string;
}

@Component({
  selector: 'app-root',
  imports: [RouterLink, RouterOutlet, NzIconModule, NzLayoutModule, NzMenuModule, RouterLinkActive, NzBreadCrumbModule, NzAvatarModule, NzDropDownModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  isCollapsed = false;
  breadcrumbs: BreadcrumbItem[] = [];
  currentUser = {
    name: 'Ahmed Benali',
    email: '<EMAIL>',
    tenant: 'Traiteria Premium',
    avatar: 'AB'
  };

  constructor(private router: Router, private activatedRoute: ActivatedRoute) {}

  ngOnInit() {
    // Écouter les changements de route pour mettre à jour le breadcrumb
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateBreadcrumbs();
      });

    // Initialiser le breadcrumb
    this.updateBreadcrumbs();
  }

  isEventsListActive(): boolean {
    const url = this.router.url;
    return url === '/events' || url.startsWith('/events/edit/');
  }

  isClientsListActive(): boolean {
    const url = this.router.url;
    return url === '/clients' || url.startsWith('/clients/edit/');
  }

  private updateBreadcrumbs() {
    this.breadcrumbs = [];

    // Construire le breadcrumb à partir des données des routes
    let route = this.activatedRoute;
    let url = '';

    // Remonter jusqu'à la route racine
    while (route.parent) {
      route = route.parent;
    }

    // Parcourir les routes enfants
    const breadcrumbData = this.getBreadcrumbData(route);

    // Toujours commencer par Accueil si on n'est pas déjà dessus
    if (this.router.url !== '/welcome') {
      this.breadcrumbs.push({ label: 'Accueil', url: '/welcome', icon: 'home' });
    }

    // Ajouter les breadcrumbs des routes
    breadcrumbData.forEach((item, index) => {
      if (index === breadcrumbData.length - 1) {
        // Dernier élément - pas de lien
        this.breadcrumbs.push({ label: item.label, icon: item.icon });
      } else {
        // Éléments intermédiaires - avec lien
        this.breadcrumbs.push({ label: item.label, url: item.url, icon: item.icon });
      }
    });
  }

  private getBreadcrumbData(route: ActivatedRoute): any[] {
    const breadcrumbs: any[] = [];
    const url = this.router.url;

    if (url === '/welcome') {
      breadcrumbs.push({ label: 'Dashboard', icon: 'home' });
    } else if (url.startsWith('/events')) {
      breadcrumbs.push({ label: 'Événements', url: '/events', icon: 'calendar' });

      if (url === '/events/new') {
        breadcrumbs.push({ label: 'Nouvel événement', icon: 'plus' });
      } else if (url.startsWith('/events/edit/')) {
        breadcrumbs.push({ label: 'Modifier événement', icon: 'edit' });
      } else if (url === '/events') {
        breadcrumbs[breadcrumbs.length - 1] = { label: 'Liste des événements', icon: 'calendar' };
      }
    } else if (url.startsWith('/clients')) {
      breadcrumbs.push({ label: 'Clients', url: '/clients', icon: 'user' });

      if (url === '/clients/new') {
        breadcrumbs.push({ label: 'Nouveau client', icon: 'plus' });
      } else if (url.startsWith('/clients/edit/')) {
        breadcrumbs.push({ label: 'Modifier client', icon: 'edit' });
      } else if (url === '/clients') {
        breadcrumbs[breadcrumbs.length - 1] = { label: 'Liste des clients', icon: 'user' };
      }
    }

    return breadcrumbs;
  }
}
