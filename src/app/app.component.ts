import { Component, OnInit } from '@angular/core';
import {RouterLink, RouterLinkActive, RouterOutlet, Router, NavigationEnd} from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  imports: [RouterLink, RouterOutlet, NzIconModule, NzLayoutModule, NzMenuModule, RouterLinkActive],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  isCollapsed = false;

  constructor(private router: Router) {}

  isEventsListActive(): boolean {
    const url = this.router.url;
    return url === '/events' || url.startsWith('/events/edit/');
  }
}
