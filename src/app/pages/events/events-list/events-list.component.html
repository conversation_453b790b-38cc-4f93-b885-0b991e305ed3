<div class="events-list-container">
  <!-- Header avec titre et bouton d'ajout -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">Gestion des événements</h2>
      <div class="header-actions">
        <button nz-button nzType="primary" routerLink="/events/new" class="add-btn">
          <nz-icon nzType="plus"></nz-icon>
          Nouvel événement
        </button>

        @if (selectedEvents.length > 0) {
          <button
            nz-button
            nzType="default"
            nzDanger
            nz-popconfirm
            nzPopconfirmTitle="Êtes-vous sûr de vouloir supprimer ces événements ?"
            (nzOnConfirm)="deleteSelectedEvents()">
            <nz-icon nzType="delete"></nz-icon>
            Supprimer ({{ selectedEvents.length }})
          </button>
        }
      </div>
    </div>
  </div>

  <nz-card class="events-card">

    <!-- Filtres -->
    <div class="filters-section">
      <div class="filters-grid">
        <div class="filter-item">
          <nz-input-group nzPrefixIcon="search">
            <input
              nz-input
              placeholder="Rechercher..."
              [(ngModel)]="searchText"
              (ngModelChange)="updateSingleChecked()">
          </nz-input-group>
        </div>

        <div class="filter-item">
          <nz-select
            nzPlaceHolder="Catégorie"
            [(ngModel)]="selectedCategory"
            (ngModelChange)="updateSingleChecked()"
            nzAllowClear>
            <nz-option nzValue="particulier" nzLabel="Particulier"></nz-option>
            <nz-option nzValue="entreprise" nzLabel="Entreprise"></nz-option>
          </nz-select>
        </div>

        <div class="filter-item">
          <nz-select
            nzPlaceHolder="Statut"
            [(ngModel)]="selectedStatus"
            (ngModelChange)="updateSingleChecked()"
            nzAllowClear>
            <nz-option nzValue="confirmed" nzLabel="Confirmé"></nz-option>
            <nz-option nzValue="pending" nzLabel="En attente"></nz-option>
            <nz-option nzValue="cancelled" nzLabel="Annulé"></nz-option>
          </nz-select>
        </div>

        <div class="filter-item">
          <nz-range-picker
            [(ngModel)]="selectedDateRange"
            (ngModelChange)="updateSingleChecked()"
            nzPlaceHolder="['Date début', 'Date fin']">
          </nz-range-picker>
        </div>

        <div class="filter-item">
          <button nz-button nzType="default" (click)="clearFilters()">
            <nz-icon nzType="clear"></nz-icon>
            Effacer
          </button>
        </div>
      </div>
    </div>

    <!-- Tableau -->
    <div class="table-container">
      <nz-table 
        #eventsTable 
        [nzData]="filteredEvents" 
        [nzPageSize]="10"
        [nzShowSizeChanger]="true"
        [nzPageSizeOptions]="[10, 20, 50]"
        nzBordered
        class="events-table">
        
        <thead>
          <tr>
            <th nzWidth="50px">
              <label 
                nz-checkbox 
                [(ngModel)]="allChecked"
                [nzIndeterminate]="indeterminate"
                (ngModelChange)="updateAllChecked()">
              </label>
            </th>
            <th [nzSortFn]="sortByType">Type</th>
            <th [nzSortFn]="sortByCategory">Catégorie</th>
            <th [nzSortFn]="sortByClient">Client</th>
            <th [nzSortFn]="sortByDate">Date</th>
            <th [nzSortFn]="sortByPersonnes">Personnes</th>
            <th [nzSortFn]="sortByVille">Ville</th>
            <th [nzSortFn]="sortByMontant">Montant</th>
            <th [nzSortFn]="sortByStatus">Statut</th>
            <th nzWidth="120px">Actions</th>
          </tr>
        </thead>
        
        <tbody>
          @for (event of eventsTable.data; track event.id) {
            <tr>
              <td>
                <label
                  nz-checkbox
                  [(ngModel)]="event.checked"
                  (ngModelChange)="updateSingleChecked()">
                </label>
              </td>
              <td>
                <strong>{{ event.type }}</strong>
              </td>
              <td>
                <nz-tag [nzColor]="event.category === 'particulier' ? 'blue' : 'purple'">
                  {{ event.category === 'particulier' ? 'Particulier' : 'Entreprise' }}
                </nz-tag>
              </td>
              <td>{{ event.client }}</td>
              <td>{{ event.date | date:'dd/MM/yyyy HH:mm' }}</td>
              <td>{{ event.nbPersonnes }}</td>
              <td>{{ event.ville }}</td>
              <td>
                <strong>{{ event.montant | number:'1.0-0' }} DH</strong>
              </td>
              <td>
                <nz-tag [nzColor]="getStatusColor(event.status)">
                  {{ getStatusText(event.status) }}
                </nz-tag>
              </td>
              <td>
                <div class="action-buttons">
                  <button
                    nz-button
                    nzType="link"
                    nzSize="small"
                    [routerLink]="['/events/edit', event.id]"
                    nz-tooltip="Modifier">
                    <nz-icon nzType="edit"></nz-icon>
                  </button>

                  <button
                    nz-button
                    nzType="link"
                    nzSize="small"
                    nzDanger
                    nz-popconfirm
                    nzPopconfirmTitle="Êtes-vous sûr de vouloir supprimer cet événement ?"
                    (nzOnConfirm)="deleteEvent(event.id)"
                    nz-tooltip="Supprimer">
                    <nz-icon nzType="delete"></nz-icon>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </nz-table>
    </div>

    <!-- Résumé -->
    @if (filteredEvents.length > 0) {
      <div class="summary-section">
        <div class="summary-stats">
          <span class="stat-item">
            <strong>{{ filteredEvents.length }}</strong> événement(s) affiché(s)
          </span>
          <span class="stat-item">
            <strong>{{ selectedEvents.length }}</strong> sélectionné(s)
          </span>
          <span class="stat-item">
            <strong>{{ totalAmount }} DH</strong>
            montant total
          </span>
        </div>
      </div>
    }

    <!-- Message si aucun résultat -->
    @if (filteredEvents.length === 0) {
      <div class="no-results">
        <nz-icon nzType="inbox" class="no-results-icon"></nz-icon>
        <h3>Aucun événement trouvé</h3>
        <p>Essayez de modifier vos critères de recherche ou créez un nouvel événement.</p>
        <button nz-button nzType="primary" routerLink="/events/new">
          <nz-icon nzType="plus"></nz-icon>
          Créer un événement
        </button>
      </div>
    }
  </nz-card>
</div>
