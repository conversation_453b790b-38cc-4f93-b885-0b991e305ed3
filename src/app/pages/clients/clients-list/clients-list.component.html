<div class="clients-list-container">
  <nz-card nzTitle="Gestion des clients" class="clients-card">

    <!-- Header avec actions -->
    <div class="header-section">
      <div class="header-actions">
        <button nz-button nzType="primary" routerLink="/clients/new">
          <nz-icon nzType="plus"></nz-icon>
          Nouveau client
        </button>

        <div class="right-actions">
          @if (selectedClients.length > 0) {
            <button
              nz-button
              nzType="default"
              nzDanger
              nz-popconfirm
              nzPopconfirmTitle="Êtes-vous sûr de vouloir supprimer ces clients ?"
              (nzOnConfirm)="deleteSelectedClients()">
              <nz-icon nzType="delete"></nz-icon>
              Supprimer ({{ selectedClients.length }})
            </button>
          }
        </div>
      </div>
    </div>

    <!-- Filtres -->
    <div class="filters-section">
      <div class="filters-grid">
        <div class="filter-item">
          <nz-input-group nzPrefixIcon="search">
            <input
              nz-input
              placeholder="Rechercher..."
              [(ngModel)]="searchText"
              (ngModelChange)="updateSingleChecked()">
          </nz-input-group>
        </div>

        <div class="filter-item">
          <nz-select
            nzPlaceHolder="Ville"
            [(ngModel)]="selectedVille"
            (ngModelChange)="updateSingleChecked()"
            nzAllowClear>
            @for (ville of villes; track ville) {
              <nz-option [nzValue]="ville" [nzLabel]="ville"></nz-option>
            }
          </nz-select>
        </div>

        <div class="filter-item">
          <nz-select
            nzPlaceHolder="Statut"
            [(ngModel)]="selectedStatus"
            (ngModelChange)="updateSingleChecked()"
            nzAllowClear>
            <nz-option nzValue="actif" nzLabel="Actif"></nz-option>
            <nz-option nzValue="inactif" nzLabel="Inactif"></nz-option>
          </nz-select>
        </div>

        <div class="filter-item">
          <nz-range-picker
            [(ngModel)]="selectedDateRange"
            (ngModelChange)="updateSingleChecked()"
            nzPlaceHolder="['Date début', 'Date fin']">
          </nz-range-picker>
        </div>

        <div class="filter-item">
          <button nz-button nzType="default" (click)="clearFilters()">
            <nz-icon nzType="clear"></nz-icon>
            Effacer
          </button>
        </div>
      </div>
    </div>

    <!-- Tableau -->
    <div class="table-container">
      <nz-table
        #clientsTable
        [nzData]="filteredClients"
        [nzPageSize]="10"
        [nzShowSizeChanger]="true"
        [nzPageSizeOptions]="[10, 20, 50]"
        nzBordered
        class="clients-table">

        <thead>
          <tr>
            <th nzWidth="50px">
              <label
                nz-checkbox
                [(ngModel)]="allChecked"
                [nzIndeterminate]="indeterminate"
                (ngModelChange)="updateAllChecked()">
              </label>
            </th>
            <th [nzSortFn]="sortByNom">Nom</th>
            <th [nzSortFn]="sortByPrenom">Prénom</th>
            <th [nzSortFn]="sortByCin">CIN</th>
            <th [nzSortFn]="sortByVille">Ville</th>
            <th [nzSortFn]="sortByTelephone">Téléphone</th>
            <th [nzSortFn]="sortByEmail">Email</th>
            <th [nzSortFn]="sortByEvenements">Événements</th>
            <th nzWidth="120px">Actions</th>
          </tr>
        </thead>

        <tbody>
          @for (client of clientsTable.data; track client.id) {
            <tr>
              <td>
                <label
                  nz-checkbox
                  [(ngModel)]="client.checked"
                  (ngModelChange)="updateSingleChecked()">
                </label>
              </td>
              <td>
                <strong>{{ client.nom }}</strong>
              </td>
              <td>{{ client.prenom }}</td>
              <td>{{ client.cin }}</td>
              <td>{{ client.ville }}</td>
              <td>{{ getClientPhone(client) }}</td>
              <td>{{ client.email }}</td>
              <td>
                <strong>{{ client.nombreEvenements }}</strong>
              </td>
              <td>
                <div class="action-buttons">
                  <nz-dropdown [nzDropdownMenu]="clientActions" nzPlacement="bottomRight">
                    <button nz-button nzType="text" nzSize="small" class="options-btn">
                      <nz-icon nzType="more" nzRotate="90"></nz-icon>
                    </button>
                  </nz-dropdown>
                  <nz-dropdown-menu #clientActions="nzDropdownMenu">
                    <ul nz-menu>
                      <li nz-menu-item [routerLink]="['/clients/edit', client.id]">
                        <nz-icon nzType="edit"></nz-icon>
                        Modifier
                      </li>
                      <li nz-menu-item
                          nz-popconfirm
                          nzPopconfirmTitle="Êtes-vous sûr de vouloir supprimer ce client ?"
                          (nzOnConfirm)="deleteClient(client.id)"
                          class="delete-item">
                        <nz-icon nzType="delete"></nz-icon>
                        Supprimer
                      </li>
                    </ul>
                  </nz-dropdown-menu>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </nz-table>
    </div>

    <!-- Résumé -->
    @if (filteredClients.length > 0) {
      <div class="summary-section">
        <div class="summary-stats">
          <span class="stat-item">
            <strong>{{ filteredClients.length }}</strong> client(s) affiché(s)
          </span>
          <span class="stat-item">
            <strong>{{ selectedClients.length }}</strong> sélectionné(s)
          </span>
          <span class="stat-item">
            <strong>{{ totalEvents }}</strong>
            événements total
          </span>
        </div>
      </div>
    }

    <!-- Message si aucun résultat -->
    @if (filteredClients.length === 0) {
      <div class="no-results">
        <nz-icon nzType="inbox" class="no-results-icon"></nz-icon>
        <h3>Aucun client trouvé</h3>
        <p>Essayez de modifier vos critères de recherche ou créez un nouveau client.</p>
        <button nz-button nzType="primary" routerLink="/clients/new">
          <nz-icon nzType="plus"></nz-icon>
          Créer un client
        </button>
      </div>
    }
  </nz-card>
</div>
