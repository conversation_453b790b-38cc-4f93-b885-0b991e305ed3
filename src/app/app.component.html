<nz-layout class="app-layout">
  <nz-sider class="menu-sidebar"
    nzCollapsible
    nzWidth="256px"
    nzBreakpoint="md"
    [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null"
  >
    <div class="sidebar-logo">
      <a href="/" target="_self">
        <img [src]="isCollapsed ? 'assets/images/logo-min.png' : 'assets/images/logo.png'" alt="Traiteria Logo">
      </a>
    </div>
    <ul nz-menu nzTheme="light" nzMode="inline" [nzInlineCollapsed]="isCollapsed">
      <li nz-submenu nzOpen nzTitle="Dashboard" nzIcon="dashboard">
        <ul>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected" [routerLinkActiveOptions]="{exact: true}">
            <a routerLink="/welcome">Accueil</a>
          </li>
          <li nz-menu-item>
            <a>Statistiques</a>
          </li>
          <li nz-menu-item>
            <a>Rapports</a>
          </li>
        </ul>
      </li>
      <li nz-submenu nzOpen nzTitle="Événements" nzIcon="calendar">
        <ul>
          <li nz-menu-item [class.ant-menu-item-selected]="isEventsListActive()">
            <a routerLink="/events">Liste des événements</a>
          </li>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected" [routerLinkActiveOptions]="{exact: true}">
            <a routerLink="/events/new">Nouvel événement</a>
          </li>
        </ul>
      </li>
      <li nz-submenu nzOpen nzTitle="Clients" nzIcon="user">
        <ul>
          <li nz-menu-item [nzSelected]="isClientsListActive()">
            <a routerLink="/clients">Liste des clients</a>
          </li>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected">
            <a routerLink="/clients/new">Nouveau client</a>
          </li>
        </ul>
      </li>

      <li nz-submenu nzOpen nzTitle="Menus" nzIcon="menu">
        <ul>
          <li nz-menu-item [nzSelected]="isMenusListActive()">
            <a routerLink="/menus">Visualiser les menus</a>
          </li>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected">
            <a routerLink="/menus/new">Nouveau menu</a>
          </li>
        </ul>
      </li>

      <li nz-submenu nzOpen nzTitle="Services" nzIcon="setting">
        <ul>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected">
            <a routerLink="/services/particulier">Particulier</a>
          </li>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected">
            <a routerLink="/services/entreprise">Entreprise</a>
          </li>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected">
            <a routerLink="/services/evenements-speciaux">Événements spéciaux</a>
          </li>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected">
            <a routerLink="/services/location-materiel">Location de matériel</a>
          </li>
        </ul>
      </li>
    </ul>

    <!-- Tenant info collée en bas -->
    <div class="tenant-info">
      <div class="tenant-card">
        <div class="tenant-icon">
          <nz-icon nzType="crown"></nz-icon>
        </div>
        <div class="tenant-details">
          <div class="tenant-name">{{ currentUser.tenant }}</div>
          <div class="tenant-status">Plan Premium</div>
        </div>
        <div class="tenant-actions">
          <nz-icon nzType="setting" class="settings-icon"></nz-icon>
        </div>
      </div>
    </div>
  </nz-sider>
  <nz-layout class="main-content" [class.collapsed]="isCollapsed">
    <nz-header>
      <div class="app-header">
        <span class="header-trigger" (click)="isCollapsed = !isCollapsed">
          <nz-icon class="trigger" [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'" />
        </span>
        <div class="breadcrumb-container">
          <nz-breadcrumb class="apple-breadcrumb">
            @for (item of breadcrumbs; track item.label; let isLast = $last) {
              <nz-breadcrumb-item>
                @if (!isLast && item.url) {
                  <a [routerLink]="item.url">
                    @if (item.icon) {
                      <nz-icon [nzType]="item.icon"></nz-icon>
                    }
                    {{ item.label }}
                  </a>
                } @else {
                  <span class="current-page">
                    @if (item.icon) {
                      <nz-icon [nzType]="item.icon"></nz-icon>
                    }
                    {{ item.label }}
                  </span>
                }
                @if (!isLast) {
                  <span class="breadcrumb-separator">/</span>
                }
              </nz-breadcrumb-item>
            }
          </nz-breadcrumb>
        </div>
        <div class="user-section">
          <div class="user-info" nz-dropdown [nzDropdownMenu]="userMenu" nzTrigger="click" nzPlacement="bottomRight">
            <nz-avatar [nzText]="currentUser.avatar" [nzSize]="32" class="user-avatar"></nz-avatar>
            <div class="user-details">
              <span class="user-name">{{ currentUser.name }}</span>
              <span class="user-tenant">{{ currentUser.tenant }}</span>
            </div>
            <nz-icon nzType="down" class="dropdown-icon"></nz-icon>
          </div>
          <nz-dropdown-menu #userMenu="nzDropdownMenu">
            <ul nz-menu nzSelectable="false">
              <li nz-menu-item>
                <nz-icon nzType="user"></nz-icon>
                Profil
              </li>
              <li nz-menu-item>
                <nz-icon nzType="setting"></nz-icon>
                Paramètres
              </li>
              <li nz-menu-divider></li>
              <li nz-menu-item>
                <nz-icon nzType="logout"></nz-icon>
                Déconnexion
              </li>
            </ul>
          </nz-dropdown-menu>
        </div>
      </div>
    </nz-header>
    <nz-content>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>
