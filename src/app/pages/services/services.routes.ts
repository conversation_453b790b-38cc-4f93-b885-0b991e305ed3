import { Routes } from '@angular/router';

export const SERVICES_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'particulier',
    pathMatch: 'full'
  },
  {
    path: 'particulier',
    loadComponent: () => import('./services-config/services-config.component').then(m => m.ServicesConfigComponent),
    data: { breadcrumb: { label: 'Services Particulier' } }
  },
  {
    path: 'entreprise',
    loadComponent: () => import('./services-config/services-config.component').then(m => m.ServicesConfigComponent),
    data: { breadcrumb: { label: 'Services Entreprise' } }
  },
  {
    path: 'evenements-speciaux',
    loadComponent: () => import('./services-config/services-config.component').then(m => m.ServicesConfigComponent),
    data: { breadcrumb: { label: 'Services Événements Spéciaux' } }
  },
  {
    path: 'location-materiel',
    loadComponent: () => import('./services-config/services-config.component').then(m => m.ServicesConfigComponent),
    data: { breadcrumb: { label: 'Services Location Matériel' } }
  }
];
