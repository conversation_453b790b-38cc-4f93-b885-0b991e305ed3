.reports-container {
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
  min-height: 100vh;
}

/* Header */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--apple-gray-600);
}

.header-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.date-range-picker,
.period-select {
  width: 150px;
}

/* Sections */
.reports-section,
.generated-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.section-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
}

/* Templates de rapports */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.template-card {
  border-radius: 16px;
  border: 1px solid var(--apple-gray-200);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.template-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.template-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.template-header {
  display: flex;
  gap: 16px;
}

.template-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.template-info {
  flex: 1;
  min-width: 0;
}

.template-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.template-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--apple-gray-600);
  line-height: 1.5;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.template-category {
  padding: 4px 8px;
  background: var(--apple-gray-100);
  color: var(--apple-gray-700);
  border-radius: 6px;
  font-weight: 500;
}

.template-last-generated {
  color: var(--apple-gray-500);
}

.template-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.schedule-btn {
  border-radius: 8px !important;
  border: 1px solid var(--apple-gray-300) !important;
  color: var(--apple-gray-700) !important;
}

.schedule-btn:hover {
  border-color: var(--apple-grenat) !important;
  color: var(--apple-grenat) !important;
}

.generate-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
}

/* Table des rapports générés */
.reports-table-card {
  border-radius: 16px;
  border: 1px solid var(--apple-gray-200);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.report-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #ff4d4f;
  font-size: 16px;
}

.report-type {
  padding: 4px 8px;
  background: var(--apple-gray-100);
  color: var(--apple-gray-700);
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.report-actions {
  display: flex;
  gap: 4px;
}

.progress-bar {
  margin-top: 4px;
  width: 100px;
}

/* État vide */
.empty-reports {
  border-radius: 16px;
  border: 2px dashed var(--apple-gray-300);
  background: var(--apple-white);
}

.empty-content {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-gray-800);
}

.empty-content p {
  margin: 0;
  color: var(--apple-gray-600);
}

/* Conseils */
.tips-card {
  border-radius: 16px;
  border: 1px solid var(--apple-gray-200);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.tips-header {
  margin-bottom: 24px;
}

.tips-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.tip-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: var(--apple-gray-50);
  border-radius: 12px;
  border: 1px solid var(--apple-gray-200);
}

.tip-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.tip-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.tip-content p {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
  line-height: 1.5;
}

/* Styles ng-zorro personnalisés */
:host ::ng-deep .reports-table-card .ant-table-thead > tr > th {
  background: var(--apple-gray-50) !important;
  border-bottom: 2px solid var(--apple-gray-200) !important;
  color: var(--apple-grenat) !important;
  font-weight: 600 !important;
}

:host ::ng-deep .reports-table-card .ant-table-tbody > tr:hover > td {
  background: var(--apple-grenat-soft) !important;
}

/* Responsive */
@media (max-width: 1200px) {
  .templates-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .reports-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-filters {
    justify-content: center;
  }
  
  .tips-grid {
    grid-template-columns: 1fr;
  }
  
  .template-actions {
    justify-content: center;
  }
}
