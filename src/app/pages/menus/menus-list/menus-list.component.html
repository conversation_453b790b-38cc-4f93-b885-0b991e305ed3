<div class="menus-container">
  <!-- Header avec titre et bouton d'ajout -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">Gestion des menus</h2>
      <button nz-button nzType="primary" routerLink="/menus/new" class="add-btn">
        <nz-icon nzType="plus"></nz-icon>
        Nouveau menu
      </button>
    </div>
  </div>

  <!-- Barre de recherche -->
  <div class="search-section">
    <div class="search-row">
      <div class="search-input-group">
        <input
          nz-input
          [(ngModel)]="searchTerm"
          (ngModelChange)="onSearchChange()"
          placeholder="Rechercher un menu..."
          class="search-input" />
        @if (searchTerm) {
          <button
            nz-button
            nzType="text"
            nzSize="small"
            (click)="clearSearch()"
            class="clear-search-btn">
            <nz-icon nzType="close"></nz-icon>
          </button>
        }
      </div>
    </div>
  </div>

  <!-- Grille des menus -->
  <div class="menus-grid">
    <div nz-row [nzGutter]="[24, 24]">
      @for (menu of filteredMenus; track menu.id) {
        <div nz-col nzXs="24" nzSm="12" nzMd="8" nzLg="8" nzXl="6">
          <nz-card 
            class="menu-card"
            [nzActions]="[editAction, deleteAction]">
            
            <!-- Header du menu -->
            <div class="menu-header">
              <h3 class="menu-title">{{ menu.title }}</h3>
            </div>

            <!-- Liste des éléments du menu -->
            <div class="menu-items">
              @for (item of menu.items; track item.id) {
                <div class="menu-item">
                  <nz-icon 
                    nzType="check-circle" 
                    [class.checked]="item.checked"
                    class="check-icon">
                  </nz-icon>
                  <span class="item-text">{{ item.name }}</span>
                </div>
              }
            </div>

            <!-- Actions template -->
            <ng-template #editAction>
              <button 
                nz-button 
                nzType="link" 
                [routerLink]="['/menus/edit', menu.id]"
                nz-tooltip="Modifier">
                <nz-icon nzType="edit"></nz-icon>
              </button>
            </ng-template>

            <ng-template #deleteAction>
              <button
                nz-button
                nzType="link"
                nzDanger
                nz-popconfirm
                nzPopconfirmTitle="Êtes-vous sûr de vouloir supprimer ce menu ?"
                nz-tooltip="Supprimer">
                <nz-icon nzType="delete"></nz-icon>
              </button>
            </ng-template>

          </nz-card>
        </div>
      }
    </div>
  </div>

  <!-- Message si aucun résultat -->
  @if (filteredMenus.length === 0) {
    <div class="no-results">
      <nz-card class="empty-card">
        <div class="empty-content">
          <nz-icon nzType="inbox" class="empty-icon"></nz-icon>
          <h3>Aucun menu trouvé</h3>
          <p>Essayez de modifier vos critères de recherche ou créez un nouveau menu.</p>
          <button nz-button nzType="primary" routerLink="/menus/new">
            <nz-icon nzType="plus"></nz-icon>
            Créer un menu
          </button>
        </div>
      </nz-card>
    </div>
  }
</div>
