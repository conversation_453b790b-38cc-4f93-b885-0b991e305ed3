import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';

@Component({
  selector: 'app-menu-form',
  standalone: true,
  imports: [CommonModule, NzCardModule],
  template: `
    <div class="menu-form-container">
      <!-- Header avec titre -->
      <div class="page-header">
        <div class="header-content">
          <h2 class="page-title">Formulaire de menu</h2>
        </div>
      </div>

      <nz-card class="menu-form-card">
        <p>Formulaire de création/modification de menu à implémenter</p>
      </nz-card>
    </div>
  `,
  styles: [`
    .menu-form-container {
      padding: 24px 32px 16px 32px;
      max-width: 1000px;
      margin: 0 auto;
      background: var(--apple-gray-50);
    }

    /* Header */
    .page-header {
      margin-bottom: 24px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .page-title {
      margin: 0;
      font-size: 28px;
      font-weight: 700;
      color: var(--apple-black);
      background: var(--gradient-grenat);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .menu-form-card {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      border: 1px solid #f0f0f0;
    }
  `]
})
export class MenuFormComponent {}
