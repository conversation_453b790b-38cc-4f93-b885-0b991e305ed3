.client-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px 32px 16px 32px; /* <PERSON><PERSON><PERSON> padding que event-form */
}

.client-form-card {
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-lg);
  background: var(--apple-white);
  border: 1px solid var(--apple-gray-200);
}

/* Formulaire */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-form-item-label > label {
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.ant-form-item-label > label.ant-form-item-required::before {
  color: #8A3A45;
}

/* Inputs */
.ant-input,
.ant-input:hover {
  border-radius: 8px;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #8A3A45;
  box-shadow: 0 0 0 2px rgba(138, 58, 69, 0.1);
}

.ant-input.error {
  border-color: #ff4d4f;
}

.ant-input.error:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
}

/* Placeholders */
.ant-input::placeholder {
  color: #bfbfbf;
  font-style: italic;
}

/* Actions du formulaire */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  gap: 12px;
}

.form-actions button {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
}

/* Aide */
.form-help {
  margin-top: 24px;
  padding: 16px;
  background: linear-gradient(135deg, #f6f8fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid #8A3A45;
}

.form-help h4 {
  margin: 0 0 12px 0;
  color: #8A3A45;
  font-size: 14px;
  font-weight: 600;
}

.form-help ul {
  margin: 0;
  padding-left: 20px;
}

.form-help li {
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.form-help li:last-child {
  margin-bottom: 0;
}

.form-help strong {
  color: #8A3A45;
  font-weight: 600;
}

/* Messages d'erreur */
.ant-form-item-explain-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

/* Responsive */
@media (max-width: 768px) {
  .client-form-container {
    padding: 16px;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .form-actions button {
    width: 100%;
  }
}

/* États des boutons */
.ant-btn-primary {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-btn-primary:hover {
  background-color: #A04A56;
  border-color: #A04A56;
}

.ant-btn-primary:focus {
  background-color: #A04A56;
  border-color: #A04A56;
  box-shadow: 0 0 0 2px rgba(138, 58, 69, 0.1);
}

.ant-btn-primary[disabled] {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

/* Animation de chargement */
.ant-btn-loading {
  pointer-events: none;
}

/* Amélioration de l'accessibilité */
.ant-input:focus-visible {
  outline: 2px solid #8A3A45;
  outline-offset: 2px;
}

/* Validation visuelle */
.ant-form-item-has-success .ant-input {
  border-color: #52c41a;
}

.ant-form-item-has-error .ant-input {
  border-color: #ff4d4f;
}

/* Styles pour les champs requis */
.ant-form-item-required .ant-form-item-label > label::after {
  content: '';
}

/* Amélioration des textarea */
textarea.ant-input {
  resize: vertical;
  min-height: 80px;
}

/* Styles pour les inputs numériques */
input[type="tel"],
input[maxlength="10"] {
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

/* Transformation automatique pour CIN */
input[style*="text-transform: uppercase"] {
  font-weight: 500;
  letter-spacing: 1px;
}

/* Hover effects */
.ant-input:hover:not(:focus):not(.error) {
  border-color: #8A3A45;
}

/* Focus states améliorés */
.ant-form-item:focus-within .ant-form-item-label > label {
  color: #8A3A45;
}

/* Scrollbar personnalisée pour le container */
.client-form-container::-webkit-scrollbar {
  width: 6px;
}

.client-form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.client-form-container::-webkit-scrollbar-thumb {
  background: #8A3A45;
  border-radius: 3px;
}

.client-form-container::-webkit-scrollbar-thumb:hover {
  background: #A04A56;
}
