.clients-list-container {
  padding: 24px 32px 16px 32px; /* BEAUCOUP plus d'espace sur les côtés */
  max-width: 100%; /* Utiliser toute la largeur disponible */
  margin: 0;
}

.clients-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

/* Header section */
.header-section {
  margin-bottom: 24px;
}

.header-actions {
  display: flex;
  gap: 12px;
  justify-content: space-between;
  align-items: center;
}

.header-actions button {
  border-radius: 8px;
  font-weight: 500;
}

.right-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Filters section */
.filters-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
  gap: 16px;
  align-items: center;
}

.filter-item {
  width: 100%;
}

.filters-section .ant-input,
.filters-section .ant-select-selector,
.filters-section .ant-picker,
.filters-section .ant-select,
.filters-section .ant-input-group {
  border-radius: 8px;
  width: 100% !important;
}

/* Table container */
.table-container {
  margin-bottom: 16px;
}

.clients-table {
  background: var(--apple-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--apple-gray-200) !important;
}

/* Table styles */
.clients-table .ant-table {
  border-radius: 12px;
}

.clients-table .ant-table-thead > tr > th {
  background: #f5f5f7 !important; /* Gris Apple doux */
  color: #1d1d1f !important;
  font-weight: 600;
  border-bottom: 1px solid #e5e5e7;
  text-align: left;
  border-radius: 0;
}

.clients-table .ant-table-thead > tr > th:first-child {
  border-top-left-radius: 12px;
}

.clients-table .ant-table-thead > tr > th:last-child {
  border-top-right-radius: 12px;
}

.clients-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
  vertical-align: middle;
}

.clients-table .ant-table-tbody > tr:hover > td {
  background: #f8f9fa;
}

.clients-table .ant-table-tbody > tr:last-child > td:first-child {
  border-bottom-left-radius: 12px;
}

.clients-table .ant-table-tbody > tr:last-child > td:last-child {
  border-bottom-right-radius: 12px;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons button {
  border-radius: 6px;
}

/* Summary section */
.summary-section {
  margin-top: 16px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-stats {
  display: flex;
  gap: 24px;
  justify-content: center;
  align-items: center;
}

.stat-item {
  font-size: 14px;
  color: #495057;
}

.stat-item strong {
  color: #212529;
  font-weight: 600;
}

/* No results */
.no-results {
  text-align: center;
  padding: 48px 24px;
  color: #6c757d;
}

.no-results-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-results h3 {
  margin: 16px 0 8px 0;
  color: #495057;
  font-weight: 600;
}

.no-results p {
  margin-bottom: 24px;
  color: #6c757d;
}

/* Responsive */
@media (max-width: 1200px) {
  .filters-grid {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .clients-list-container {
    padding: 16px;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .header-actions {
    flex-direction: column;
    gap: 16px;
  }

  .summary-stats {
    flex-direction: column;
    gap: 12px;
  }
}
