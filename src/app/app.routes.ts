import { Routes } from '@angular/router';

export const routes: Routes = [
  { path: '', pathMatch: 'full', redirectTo: '/welcome' },
  {
    path: 'welcome',
    loadChildren: () => import('./pages/welcome/welcome.routes').then(m => m.WELCOME_ROUTES),
    data: { breadcrumb: { label: 'Accueil', icon: 'home' } }
  },
  {
    path: 'events',
    loadChildren: () => import('./pages/events/events.routes').then(m => m.EVENTS_ROUTES),
    data: { breadcrumb: { label: 'Événements', icon: 'calendar' } }
  },
  {
    path: 'clients',
    loadChildren: () => import('./pages/clients/clients.routes').then(m => m.CLIENTS_ROUTES),
    data: { breadcrumb: { label: 'Clients', icon: 'user' } }
  },
  {
    path: 'menus',
    loadChildren: () => import('./pages/menus/menus.routes').then(m => m.MENUS_ROUTES),
    data: { breadcrumb: { label: 'Menus', icon: 'menu' } }
  },
  {
    path: 'services',
    loadChildren: () => import('./pages/services/services.routes').then(m => m.SERVICES_ROUTES),
    data: { breadcrumb: { label: 'Services', icon: 'setting' } }
  }
];
