.calendar-container {
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.create-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.create-btn:hover:not(:disabled) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3) !important;
}

/* <PERSON>tes */
.calendar-card,
.legend-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.calendar-card {
  overflow: hidden;
}

/* Wrapper du calendrier */
.calendar-wrapper {
  padding: 0;
}

/* Styles FullCalendar personnalisés */
:host ::ng-deep .fc {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

:host ::ng-deep .fc-header-toolbar {
  padding: 20px 24px;
  background: var(--apple-gray-50);
  border-bottom: 1px solid var(--apple-gray-200);
  margin-bottom: 0 !important;
}

:host ::ng-deep .fc-toolbar-title {
  font-size: 24px !important;
  font-weight: 700 !important;
  color: var(--apple-grenat) !important;
}

:host ::ng-deep .fc-button-primary {
  background: var(--apple-white) !important;
  border: 1px solid var(--apple-gray-300) !important;
  color: var(--apple-gray-700) !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .fc-button-primary:hover {
  background: var(--apple-grenat) !important;
  border-color: var(--apple-grenat) !important;
  color: var(--apple-white) !important;
  transform: translateY(-1px) !important;
}

:host ::ng-deep .fc-button-primary:focus {
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.2) !important;
}

:host ::ng-deep .fc-button-active {
  background: var(--apple-grenat) !important;
  border-color: var(--apple-grenat) !important;
  color: var(--apple-white) !important;
}

:host ::ng-deep .fc-daygrid-day {
  transition: background-color 0.2s ease;
}

:host ::ng-deep .fc-daygrid-day:hover {
  background: var(--apple-grenat-soft) !important;
}

:host ::ng-deep .fc-day-today {
  background: var(--apple-grenat-pale) !important;
}

:host ::ng-deep .fc-event {
  border-radius: 6px !important;
  border: none !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 2px 6px !important;
  margin: 1px 0 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .fc-event:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

:host ::ng-deep .fc-event-title {
  font-weight: 600 !important;
}

:host ::ng-deep .fc-event-time {
  font-weight: 400 !important;
  opacity: 0.9;
}

:host ::ng-deep .fc-more-link {
  color: var(--apple-grenat) !important;
  font-weight: 500 !important;
}

:host ::ng-deep .fc-col-header-cell {
  background: var(--apple-gray-50) !important;
  border-color: var(--apple-gray-200) !important;
  font-weight: 600 !important;
  color: var(--apple-gray-700) !important;
  text-transform: uppercase !important;
  font-size: 11px !important;
  letter-spacing: 0.5px !important;
}

:host ::ng-deep .fc-daygrid-day-number {
  color: var(--apple-gray-800) !important;
  font-weight: 500 !important;
  padding: 8px !important;
}

:host ::ng-deep .fc-scrollgrid {
  border-color: var(--apple-gray-200) !important;
  border-radius: 0 0 12px 12px !important;
  overflow: hidden !important;
}

:host ::ng-deep .fc-scrollgrid-section > td {
  border-color: var(--apple-gray-200) !important;
}

/* Légende */
.legend-content {
  padding: 16px 0;
}

.legend-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--apple-grenat);
}

.legend-items {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.legend-info {
  padding: 12px 16px;
  background: var(--apple-gray-50);
  border-radius: 8px;
  border-left: 4px solid var(--apple-grenat);
}

.legend-info p {
  margin: 0;
  color: var(--apple-gray-700);
  font-size: 14px;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .calendar-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .legend-items {
    justify-content: center;
  }
  
  :host ::ng-deep .fc-header-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  :host ::ng-deep .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
  }
}
