:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  height: 100vh;
  background: var(--apple-gray-50);
}

.menu-sidebar {
  position: fixed !important;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  height: 100vh !important;
  overflow-y: auto;
  border-right: 1px solid var(--apple-gray-200) !important;
  background: var(--apple-grenat-soft) !important;
}

.header-trigger {
  height: 64px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  color: var(--apple-gray-600);
  display: none !important; /* Cacher le bouton de collapse */
}

.trigger:hover {
  color: var(--apple-black);
  transform: scale(1.05);
}

.sidebar-logo {
  position: relative;
  height: 80px;
  padding: 16px 24px;
  overflow: hidden;
  background: var(--apple-grenat-soft);
  transition: all .3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-logo a {
  display: flex;
  align-items: center;
  justify-content: center; /* Centrer le logo */
  text-decoration: none;
  width: 100%;
}

/* Styles spécifiques pour le logo selon l'état de la sidebar */
.ant-layout-sider-collapsed .sidebar-logo {
  height: 64px;
  padding: 16px 8px;
  background: var(--apple-grenat-soft) !important;
}

/* Pas de changement de direction nécessaire */

.ant-layout-sider-collapsed .sidebar-logo img {
  height: 32px;
  max-width: 32px;
}

.ant-layout-sider:not(.ant-layout-sider-collapsed) .sidebar-logo img {
  height: 48px; /* Logo plus grand quand ouvert */
  max-width: 150px;
}

.sidebar-logo img {
  display: block;
  width: auto;
  transition: all .3s;
  object-fit: contain; /* Maintenir les proportions */
  margin: 0 auto; /* Centrer le logo */
}

/* Styles h1 supprimés car plus de texte */

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  border-bottom: 1px solid var(--apple-gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Breadcrumb Container */
.breadcrumb-container {
  flex: 1;
  padding: 0 24px;
  display: flex;
  align-items: center;
}

/* Apple-style Breadcrumb */
.apple-breadcrumb {
  margin: 0 !important;
  font-size: 14px;
  font-weight: 500;
  display: flex !important;
  align-items: center !important;
}

:host ::ng-deep .apple-breadcrumb ol {
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
  padding: 0 !important;
}

:host ::ng-deep .apple-breadcrumb .ant-breadcrumb-separator {
  color: var(--apple-gray-600) !important;
  margin: 0 8px !important;
  font-size: 12px;
  display: flex !important;
  align-items: center !important;
}

:host ::ng-deep .apple-breadcrumb .ant-breadcrumb-link {
  color: var(--apple-grenat) !important;
  text-decoration: none;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex !important;
  align-items: center !important;
  gap: 6px;
  line-height: 1 !important;
}

:host ::ng-deep .apple-breadcrumb .ant-breadcrumb-link:hover {
  color: var(--apple-grenat-dark) !important;
  transform: translateY(-1px);
}

:host ::ng-deep .apple-breadcrumb .ant-breadcrumb-link .anticon {
  font-size: 12px;
  color: inherit;
  display: flex !important;
  align-items: center !important;
}

:host ::ng-deep .apple-breadcrumb li {
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
}

.current-page {
  color: var(--apple-gray-900) !important;
  font-weight: 600;
  display: flex !important;
  align-items: center !important;
  gap: 6px;
  line-height: 1 !important;
}

.current-page .anticon {
  font-size: 12px;
  color: var(--apple-gray-600);
  display: flex !important;
  align-items: center !important;
}

/* Layout principal qui s'adapte à la sidebar fixe */
.main-content {
  margin-left: 256px; /* Largeur de la sidebar ouverte */
  transition: margin-left 0.3s ease;
  min-height: 100vh;
}

.main-content.collapsed {
  margin-left: 80px; /* Largeur de la sidebar fermée */
}

nz-content {
  margin: 0;
  background: #fafafa;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
}

.inner-content {
  padding: 0;
  background: #fafafa;
  min-height: calc(100vh - 64px);
}

/* STYLES SIDEBAR - APPROCHE BRUTALE POUR ÉCRASER TOUT */

/* Background du menu */
.menu-sidebar .ant-menu,
.menu-sidebar .ant-menu-light,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-light {
  background: var(--apple-grenat-soft) !important;
}

/* TOUS les éléments de submenu - état normal */
.menu-sidebar li[nz-submenu] .ant-menu-submenu-title,
.menu-sidebar li[nz-submenu] .ant-menu-submenu-title *,
.menu-sidebar.ant-layout-sider-collapsed li[nz-submenu] .ant-menu-submenu-title,
.menu-sidebar.ant-layout-sider-collapsed li[nz-submenu] .ant-menu-submenu-title * {
  color: var(--apple-black) !important;
  background: transparent !important;
}

/* TOUS les éléments de submenu - état hover - PAS de background */
.menu-sidebar li[nz-submenu]:hover .ant-menu-submenu-title,
.menu-sidebar li[nz-submenu] .ant-menu-submenu-title:hover,
.menu-sidebar.ant-layout-sider-collapsed li[nz-submenu]:hover .ant-menu-submenu-title,
.menu-sidebar.ant-layout-sider-collapsed li[nz-submenu] .ant-menu-submenu-title:hover {
  color: var(--apple-grenat) !important;
  background: transparent !important;
}

.menu-sidebar li[nz-submenu]:hover .ant-menu-submenu-title *,
.menu-sidebar li[nz-submenu] .ant-menu-submenu-title:hover *,
.menu-sidebar.ant-layout-sider-collapsed li[nz-submenu]:hover .ant-menu-submenu-title *,
.menu-sidebar.ant-layout-sider-collapsed li[nz-submenu] .ant-menu-submenu-title:hover * {
  color: var(--apple-grenat) !important;
}

/* TOUS les éléments de submenu - état actif/ouvert - PAS de background */
.menu-sidebar li[nz-submenu].ant-menu-submenu-open .ant-menu-submenu-title,
.menu-sidebar.ant-layout-sider-collapsed li[nz-submenu].ant-menu-submenu-open .ant-menu-submenu-title {
  color: var(--apple-grenat) !important;
  background: transparent !important;
}

.menu-sidebar li[nz-submenu].ant-menu-submenu-open .ant-menu-submenu-title *,
.menu-sidebar.ant-layout-sider-collapsed li[nz-submenu].ant-menu-submenu-open .ant-menu-submenu-title * {
  color: var(--apple-grenat) !important;
}

/* Menu items normaux */
.menu-sidebar .ant-menu-item,
.menu-sidebar .ant-menu-item *,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-item,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-item * {
  color: var(--apple-black) !important;
  background: transparent !important;
}

/* Menu items hover */
.menu-sidebar .ant-menu-item:hover,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-item:hover {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

.menu-sidebar .ant-menu-item:hover *,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-item:hover * {
  color: var(--apple-grenat) !important;
}

/* Menu items sélectionnés - même style que hover */
.menu-sidebar .ant-menu-item-selected,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-item-selected {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

.menu-sidebar .ant-menu-item-selected *,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-item-selected * {
  color: var(--apple-grenat) !important;
}

/* FORCER spécifiquement le mode collapsed pour les menu items */
.ant-layout-sider-collapsed .menu-sidebar .ant-menu-item-selected,
.ant-layout-sider-collapsed .menu-sidebar .ant-menu-item.ant-menu-item-selected {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

.ant-layout-sider-collapsed .menu-sidebar .ant-menu-item-selected *,
.ant-layout-sider-collapsed .menu-sidebar .ant-menu-item.ant-menu-item-selected * {
  color: var(--apple-grenat) !important;
}

/* APPROCHE NUCLÉAIRE - ::ng-deep pour forcer ABSOLUMENT */
:host ::ng-deep .menu-sidebar .ant-menu-submenu-title {
  color: var(--apple-black) !important;
}

:host ::ng-deep .menu-sidebar .ant-menu-submenu-title:hover {
  color: var(--apple-grenat) !important;
  background: transparent !important;
}

:host ::ng-deep .menu-sidebar .ant-menu-submenu-title:hover span,
:host ::ng-deep .menu-sidebar .ant-menu-submenu-title:hover .anticon {
  color: var(--apple-grenat) !important;
}

:host ::ng-deep .menu-sidebar .ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--apple-grenat) !important;
  background: transparent !important;
}

:host ::ng-deep .menu-sidebar .ant-menu-submenu-open > .ant-menu-submenu-title span,
:host ::ng-deep .menu-sidebar .ant-menu-submenu-open > .ant-menu-submenu-title .anticon {
  color: var(--apple-grenat) !important;
}

/* Menu items sélectionnés avec ::ng-deep */
:host ::ng-deep .menu-sidebar .ant-menu-item-selected {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

:host ::ng-deep .menu-sidebar .ant-menu-item-selected a,
:host ::ng-deep .menu-sidebar .ant-menu-item-selected span,
:host ::ng-deep .menu-sidebar .ant-menu-item-selected .anticon {
  color: var(--apple-grenat) !important;
}

/* FORCER ::ng-deep pour le mode collapsed */
:host ::ng-deep .ant-layout-sider-collapsed .menu-sidebar .ant-menu-item-selected,
:host ::ng-deep .ant-layout-sider-collapsed .menu-sidebar .ant-menu-item.ant-menu-item-selected {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

:host ::ng-deep .ant-layout-sider-collapsed .menu-sidebar .ant-menu-item-selected *,
:host ::ng-deep .ant-layout-sider-collapsed .menu-sidebar .ant-menu-item.ant-menu-item-selected * {
  color: var(--apple-grenat) !important;
}

/* Responsive pour mobile */
@media (max-width: 768px) {
  .menu-sidebar {
    position: fixed !important;
    left: -256px; /* Caché par défaut sur mobile */
    transition: left 0.3s ease;
  }

  .menu-sidebar.ant-layout-sider-collapsed {
    left: -80px;
  }

  .main-content {
    margin-left: 0 !important;
  }

  /* Overlay pour mobile quand sidebar ouverte */
  .main-content::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    z-index: 5;
    display: none;
  }
}
