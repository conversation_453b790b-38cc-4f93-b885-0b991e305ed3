<div class="menu-form-container">
  <!-- Header avec titre -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">{{ isEditMode ? 'Modifier le menu' : 'Nouveau menu' }}</h2>
    </div>
  </div>

  <nz-card class="menu-form-card">
    <form [formGroup]="menuForm" (ngSubmit)="onSubmit()" nz-form nzLayout="vertical">
      
      <!-- Nom du menu -->
      <div class="form-section">
        <div class="form-row">
          <nz-form-item class="form-item-full">
            <nz-form-label nzRequired>Nom du menu</nz-form-label>
            <nz-form-control [nzErrorTip]="getFieldError('title')">
              <input
                nz-input
                formControlName="title"
                placeholder="Ex: Menu Sultana, Menu Royal..."
                class="form-input" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Éléments du menu -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">Éléments du menu</h3>
          <button 
            type="button"
            nz-button 
            nzType="dashed" 
            (click)="addMenuItem()"
            class="add-item-btn">
            <nz-icon nzType="plus"></nz-icon>
            Ajouter un élément
          </button>
        </div>

        <div class="menu-items-list">
          @for (item of items.controls; track $index; let i = $index) {
            <div class="menu-item-row" [formGroupName]="i">
              <div class="item-checkbox">
                <nz-form-item>
                  <nz-form-control>
                    <label nz-checkbox formControlName="checked"></label>
                  </nz-form-control>
                </nz-form-item>
              </div>

              <div class="item-input">
                <nz-form-item>
                  <nz-form-control [nzErrorTip]="getItemError(i, 'name')">
                    <input 
                      nz-input 
                      formControlName="name" 
                      placeholder="Nom de l'élément du menu"
                      class="form-input" />
                  </nz-form-control>
                </nz-form-item>
              </div>

              <div class="item-actions">
                <button 
                  type="button"
                  nz-button 
                  nzType="text" 
                  nzDanger
                  (click)="removeMenuItem(i)"
                  [disabled]="items.length <= 1"
                  class="remove-item-btn">
                  <nz-icon nzType="delete"></nz-icon>
                </button>
              </div>
            </div>
          }
        </div>

        @if (items.length === 0) {
          <div class="empty-items">
            <p>Aucun élément dans le menu. Cliquez sur "Ajouter un élément" pour commencer.</p>
          </div>
        }
      </div>

      <!-- Actions -->
      <div class="form-actions">
        <button 
          type="button"
          nz-button 
          nzType="default" 
          (click)="onCancel()"
          class="cancel-btn">
          Annuler
        </button>
        
        <button 
          type="submit"
          nz-button 
          nzType="primary" 
          [nzLoading]="isSubmitting"
          [disabled]="!menuForm.valid"
          class="submit-btn">
          <nz-icon nzType="save"></nz-icon>
          {{ isEditMode ? 'Modifier' : 'Créer' }} le menu
        </button>
      </div>

    </form>
  </nz-card>
</div>
