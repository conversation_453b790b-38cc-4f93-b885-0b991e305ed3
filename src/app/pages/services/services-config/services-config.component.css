.services-config-container {
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.save-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3) !important;
}

.config-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

/* Description */
.config-description {
  margin-bottom: 24px;
  padding: 16px 20px;
  background: var(--apple-gray-50);
  border-radius: 8px;
  border: 1px solid var(--apple-gray-200);
}

.config-description p {
  margin: 0;
  color: var(--apple-gray-700);
  line-height: 1.5;
}

/* Bouton d'ajout de service */
.add-service-section {
  margin-bottom: 16px;
  text-align: center;
}

.add-service-btn {
  border-radius: 8px;
  border: 2px dashed var(--apple-gray-300);
  color: var(--apple-grenat);
  transition: all 0.2s ease;
  padding: 8px 24px;
  height: auto;
}

.add-service-btn:hover {
  border-color: var(--apple-grenat);
  background: var(--apple-grenat-soft);
}

/* Tableau matriciel avec colonnes fixes */
.matrix-table-wrapper {
  margin-bottom: 24px;
  border-radius: 12px;
  border: 1px solid var(--apple-gray-200);
  overflow: hidden;
}

.matrix-table {
  overflow-x: auto;
  position: relative;
}

.services-matrix {
  width: 100%;
  border-collapse: collapse;
  background: var(--apple-white);
}

.services-matrix th {
  background: var(--apple-gray-50);
  border: 1px solid var(--apple-gray-200);
  padding: 16px 12px;
  text-align: center;
  font-weight: 600;
  color: var(--apple-grenat);
  font-size: 14px;
}

.services-matrix .service-header {
  text-align: left;
  width: 200px;
  min-width: 200px;
}

.services-matrix .event-type-header {
  min-width: 120px;
  word-break: break-word;
  line-height: 1.3;
}

.services-matrix .actions-header {
  width: 80px;
  min-width: 80px;
  text-align: center;
}

/* Colonnes fixes */
.fixed-left {
  position: sticky;
  left: 0;
  z-index: 10;
  background: var(--apple-white);
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.fixed-right {
  position: sticky;
  right: 0;
  z-index: 10;
  background: var(--apple-white);
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
}

.services-matrix thead .fixed-left,
.services-matrix thead .fixed-right {
  background: var(--apple-gray-50);
}

.services-matrix tr:hover .fixed-left {
  background: var(--apple-grenat-pale);
}

.services-matrix tr:hover .fixed-right {
  background: var(--apple-grenat-soft);
}

.services-matrix td {
  border: 1px solid var(--apple-gray-200);
  padding: 12px;
  text-align: center;
  vertical-align: middle;
}

.services-matrix .service-name {
  text-align: left;
  font-weight: 500;
  color: var(--apple-gray-800);
  background: var(--apple-gray-25);
}

.services-matrix tr:hover td {
  background: var(--apple-grenat-soft);
}

.services-matrix tr:hover .service-name {
  background: var(--apple-grenat-pale);
}

.switch-cell {
  padding: 8px;
}

/* Cellule d'actions */
.actions-cell {
  text-align: center;
  padding: 8px;
  background: var(--apple-gray-25);
}

.delete-service-btn {
  color: var(--apple-gray-400) !important;
  transition: all 0.2s ease !important;
  border-radius: 6px !important;
}

.delete-service-btn:hover:not(:disabled) {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.1) !important;
}

.delete-service-btn:disabled {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}

:host ::ng-deep .ant-switch-checked {
  background-color: var(--apple-grenat) !important;
}

:host ::ng-deep .ant-switch:hover:not(.ant-switch-disabled) {
  background-color: var(--apple-grenat-pale) !important;
}

/* Résumé */
.config-summary {
  padding: 16px 20px;
  background: var(--apple-gray-50);
  border-radius: 8px;
  border: 1px solid var(--apple-gray-200);
}

.summary-stats {
  display: flex;
  gap: 32px;
  justify-content: center;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--apple-gray-600);
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--apple-grenat);
}

/* Responsive */
@media (max-width: 1200px) {
  .event-type-name {
    font-size: 11px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 2px;
  }
}

@media (max-width: 768px) {
  .services-config-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .service-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .service-actions {
    opacity: 1;
  }
}
