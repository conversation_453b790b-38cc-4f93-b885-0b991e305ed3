.event-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.preselected-date-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--apple-grenat-soft);
  border: 1px solid var(--apple-grenat-pale);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--apple-grenat);
}

.calendar-icon {
  color: var(--apple-grenat);
  font-size: 16px;
}

.date-text {
  color: var(--apple-grenat-dark);
  font-weight: 500;
  font-size: 14px;
  text-transform: capitalize;
}

.form-card {
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-lg);
  background: var(--apple-white);
  border: 1px solid var(--apple-gray-200);
}

/* Steps navigation - Apple style */
.steps-nav {
  margin-bottom: 24px;
  padding: 16px 24px;
  background: var(--apple-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--apple-gray-200);
}

.step-content {
  min-height: 400px;
  padding: 24px 0;
}

.step-content h3 {
  color: #8A3A45;
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
}

.step-content h4 {
  color: #8A3A45;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

.step-content h5 {
  color: #8A3A45;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

/* Étape 1 - Choix de catégorie */
.category-choice {
  width: 100%;
}

.category-choice .ant-radio-group {
  width: 100%;
  display: block;
}

.category-cards {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16px;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.category-card {
  border: 2px solid var(--apple-gray-200);
  border-radius: var(--radius-lg);
  padding: 16px 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: linear-gradient(135deg, var(--apple-white) 0%, var(--apple-gray-50) 100%);
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
}

.category-card:hover {
  border-color: var(--apple-grenat);
  box-shadow: 0 8px 25px rgba(139, 21, 56, 0.15);
  transform: translateY(-4px) scale(1.02);
  background: var(--gradient-grenat-soft);
}

/* Styles pour les cartes sélectionnées */
.category-card.selected {
  border-color: var(--apple-grenat);
  background: var(--gradient-grenat-soft);
  color: var(--apple-grenat);
  box-shadow: 0 12px 30px rgba(139, 21, 56, 0.2);
  transform: translateY(-2px) scale(1.05);
  border-width: 3px;
}

.category-card.selected .card-content h4,
.category-card.selected .card-content p {
  color: var(--apple-grenat);
}

.category-card.selected .category-icon {
  color: var(--apple-grenat) !important; /* Icône grenat sur fond doux */
}

/* Styles pour les icônes - grenat Apple */
.category-icon {
  font-size: 32px !important;
  color: var(--apple-grenat) !important; /* Grenat Apple */
  margin-bottom: 12px !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.category-card:hover .category-icon {
  color: var(--apple-grenat-light) !important;
  transform: scale(1.1) !important;
}

/* Réduire la taille des textes */
.card-content h4 {
  font-size: 16px !important;
  margin: 8px 0 4px 0 !important;
  font-weight: 600 !important;
}

.card-content p {
  font-size: 12px !important;
  margin: 0 !important;
  opacity: 0.8 !important;
}

.card-content {
  position: relative;
  z-index: 2;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.category-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #8A3A45;
  transition: all 0.3s ease;
}

.category-card.ant-radio-button-wrapper-checked .category-icon {
  color: white;
}

.card-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
}

.card-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* Sections */
.section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid #8A3A45;
}

.special-section {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-left: 4px solid #8A3A45;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.special-section h4 {
  color: #8A3A45;
  margin-bottom: 16px;
  font-weight: 600;
}

/* Styles pour les sous-sections organisées */
.options-section {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.options-section h5 {
  color: #8A3A45;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.conditional-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 8px 0;
  border-left: 4px solid #8A3A45;
}

/* Bouton nouveau client */
.new-client-btn {
  width: 100%;
  height: 48px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  color: #8A3A45;
  font-weight: 500;
  transition: all 0.3s ease;
}

.new-client-btn:hover {
  border-color: #8A3A45;
  background: rgba(138, 58, 69, 0.05);
}

/* Résumé financier */
.financial-summary {
  margin-top: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
  border-bottom: none;
}

.paid-amount {
  color: #52c41a;
}

.remaining-amount {
  color: #fa8c16;
}

/* Récapitulatif événement */
.event-summary {
  margin-top: 24px;
  background: linear-gradient(135deg, #fff7e6 0%, #fff1d6 100%);
  border: 1px solid #ffd591;
}

.summary-section {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ffd591;
}

.summary-section:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.summary-section h5 {
  margin-bottom: 4px;
  color: #8A3A45;
  font-weight: 600;
}

.summary-section p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* Actions du formulaire */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.form-actions button {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .event-form-container {
    padding: 16px;
  }

  .category-cards {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .preselected-date-info {
    flex-direction: column;
    text-align: center;
    gap: 4px;
  }

  .date-text {
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .category-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .category-card {
    max-width: 100%;
    min-width: auto;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .form-actions button {
    width: 100%;
  }
}



/* Grilles de formulaire personnalisées */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row-3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-col {
  width: 100%;
}

@media (max-width: 768px) {
  .form-row,
  .form-row-3 {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* Améliorations des formulaires */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item-label > label {
  color: #333;
  font-weight: 500;
}

.ant-input,
.ant-select-selector,
.ant-picker,
.ant-select,
.ant-input-number,
.ant-input-group {
  border-radius: 8px;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
  width: 100% !important;
}

.ant-input:hover,
.ant-select-selector:hover,
.ant-picker:hover {
  border-color: #8A3A45;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-picker-focused {
  border-color: #8A3A45;
  box-shadow: 0 0 0 2px rgba(138, 58, 69, 0.1);
}

/* Checkboxes et radios */
.ant-checkbox-wrapper,
.ant-radio-wrapper {
  font-weight: 500;
  color: #333;
}

.ant-checkbox-checked .ant-checkbox-inner,
.ant-radio-checked .ant-radio-inner {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-checkbox:hover .ant-checkbox-inner,
.ant-radio:hover .ant-radio-inner {
  border-color: #8A3A45;
}

/* Steps personnalisés */
.ant-steps-item-finish .ant-steps-item-icon {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-steps-item-process .ant-steps-item-icon {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-steps-item-finish .ant-steps-item-title {
  color: #8A3A45;
}

/* Drawer styles */
.ant-drawer-content {
  background: #fafafa;
}

/* Cards améliorées */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
}

.ant-card-head-title {
  color: #8A3A45;
  font-weight: 600;
}

/* Drag and Drop Row */
.drag-drop-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  min-height: 400px;
  margin-top: 24px;
}

/* Zones communes */
.available-services-zone,
.selected-services-zone {
  display: flex;
  flex-direction: column;
  background: var(--apple-white);
  border-radius: 12px;
  border: 2px solid var(--apple-gray-200);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.available-services-zone:hover,
.selected-services-zone:hover {
  border-color: var(--apple-grenat-pale);
  box-shadow: 0 4px 16px rgba(139, 21, 56, 0.1);
}

.zone-header {
  padding: 16px 20px;
  background: var(--apple-gray-50);
  border-bottom: 1px solid var(--apple-gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.zone-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--apple-grenat);
}

.count-badge {
  background: var(--apple-grenat);
  color: var(--apple-white);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Barre de recherche */
.search-bar {
  padding: 16px 20px;
  border-bottom: 1px solid var(--apple-gray-200);
}

.search-input {
  width: 100% !important;
  border-radius: 8px !important;
  border: 1px solid var(--apple-gray-200) !important;
  transition: all 0.2s ease !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
}

.search-input:focus {
  border-color: var(--apple-grenat) !important;
  box-shadow: 0 0 0 2px rgba(139, 21, 56, 0.1) !important;
  outline: none !important;
}

/* Listes de services */
.services-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  max-height: 300px;
}

.available-list {
  background: var(--apple-white);
}

.selected-list {
  background: var(--apple-grenat-soft);
  border: 2px dashed var(--apple-grenat-pale);
  border-radius: 8px;
  margin: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* Items de service */
.service-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: var(--apple-white);
  border: 1px solid var(--apple-gray-200);
  border-radius: 8px;
  cursor: grab;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  user-select: none;
}

.service-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 21, 56, 0.15);
  border-color: var(--apple-grenat-pale);
}

.service-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

.available-item {
  border-left: 4px solid var(--apple-grenat-pale);
}

.selected-item {
  background: var(--apple-white);
  border-left: 4px solid var(--apple-grenat);
}

.service-icon {
  font-size: 16px;
  color: var(--apple-grenat);
  flex-shrink: 0;
}

.service-label {
  flex: 1;
  font-weight: 500;
  color: var(--apple-black);
}

.service-category {
  font-size: 11px;
  color: var(--apple-gray-600);
  background: var(--apple-gray-100);
  padding: 2px 6px;
  border-radius: 4px;
  flex-shrink: 0;
}

.service-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.drag-handle {
  font-size: 14px;
  color: var(--apple-gray-400);
  cursor: grab;
}

.remove-btn,
.remove-available-btn {
  color: var(--apple-gray-400) !important;
  transition: all 0.2s ease !important;
  padding: 2px !important;
  min-width: auto !important;
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.remove-btn:hover,
.remove-available-btn:hover {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
  border-radius: 4px !important;
}

/* États vides */
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: var(--apple-gray-600);
  text-align: center;
}

.empty-drop-zone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: var(--apple-gray-600);
  text-align: center;
  flex: 1;
  min-height: 200px;
}

.empty-icon,
.drop-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: var(--apple-gray-400);
}

.empty-text,
.drop-text {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.drop-hint {
  font-size: 12px;
  color: var(--apple-gray-500);
  font-style: italic;
}

/* Barre d'ajout */
.add-service-bar {
  padding: 16px 20px;
  border-top: 1px solid var(--apple-gray-200);
  background: var(--apple-gray-50);
}

.add-service-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.add-input {
  flex: 1;
  border-radius: 6px !important;
  border: 1px solid var(--apple-gray-200) !important;
  font-size: 13px !important;
  padding: 6px 10px !important;
}

.add-input:focus {
  border-color: var(--apple-grenat) !important;
  box-shadow: 0 0 0 2px rgba(139, 21, 56, 0.1) !important;
}

.add-btn {
  background: var(--apple-grenat) !important;
  border-color: var(--apple-grenat) !important;
  color: var(--apple-white) !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  border-radius: 8px !important;
  width: 40px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

.add-btn:hover:not(:disabled) {
  background: var(--apple-grenat-dark) !important;
  border-color: var(--apple-grenat-dark) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(139, 21, 56, 0.4) !important;
}

.add-btn:disabled {
  background: var(--apple-gray-300) !important;
  border-color: var(--apple-gray-300) !important;
  color: var(--apple-gray-500) !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Animations CDK Drag */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(139, 21, 56, 0.3);
  transform: rotate(5deg);
  opacity: 0.9;
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: var(--apple-grenat-pale);
  border: 2px dashed var(--apple-grenat);
}

.cdk-drop-list-dragging .service-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.services-list.cdk-drop-list-receiving {
  background: var(--apple-grenat-soft) !important;
  border-color: var(--apple-grenat) !important;
}

/* Marge pour le thème */
.theme-section {
  margin-top: 32px !important;
}

/* Styles supprimés - remplacés par l'interface drag and drop */
