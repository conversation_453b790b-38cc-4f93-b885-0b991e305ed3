<div class="statistics-container">
  <!-- Header avec filtres -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h2 class="page-title">Statistiques détaillées</h2>
        <p class="page-subtitle">Analyse approfondie de vos performances</p>
      </div>
      <div class="header-filters">
        <nz-select 
          [(ngModel)]="selectedPeriod" 
          (ngModelChange)="onPeriodChange()"
          class="period-select">
          <nz-option nzValue="week" nzLabel="Cette semaine"></nz-option>
          <nz-option nzValue="month" nzLabel="Ce mois"></nz-option>
          <nz-option nzValue="quarter" nzLabel="Ce trimestre"></nz-option>
          <nz-option nzValue="year" nzLabel="Cette année"></nz-option>
        </nz-select>
        <button 
          nz-button 
          nzType="primary" 
          (click)="exportData()"
          class="export-btn">
          <nz-icon nzType="download"></nz-icon>
          Exporter
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques avancées -->
  <div class="advanced-stats-grid">
    <nz-card class="advanced-stat-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon" style="background: linear-gradient(135deg, #8B1538, #a91d42);">💎</div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="advancedStats.averageEventValue" 
            [nzFormatter]="formatCurrency"
            nzTitle="Valeur moyenne par événement">
          </nz-statistic>
        </div>
      </div>
    </nz-card>

    <nz-card class="advanced-stat-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon" style="background: linear-gradient(135deg, #34c759, #30d158);">📈</div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="advancedStats.conversionRate" 
            nzSuffix="%" 
            nzTitle="Taux de conversion">
          </nz-statistic>
        </div>
      </div>
    </nz-card>

    <nz-card class="advanced-stat-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon" style="background: linear-gradient(135deg, #007aff, #0056cc);">⭐</div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="advancedStats.customerSatisfaction" 
            nzSuffix="%" 
            nzTitle="Satisfaction client">
          </nz-statistic>
        </div>
      </div>
    </nz-card>

    <nz-card class="advanced-stat-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon" style="background: linear-gradient(135deg, #ff9500, #ff8c00);">🔄</div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="advancedStats.repeatCustomers" 
            nzSuffix="%" 
            nzTitle="Clients récurrents">
          </nz-statistic>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Graphiques et analyses -->
  <div class="charts-grid">
    <!-- Répartition par type d'événement -->
    <nz-card class="chart-card" [nzLoading]="isLoading">
      <div class="chart-header">
        <h3 class="chart-title">Répartition par type d'événement</h3>
        <p class="chart-subtitle">{{ getTotalEvents() }} événements au total</p>
      </div>
      <div class="chart-content">
        <div class="pie-chart-simulation">
          @for (item of eventTypesData; track item.name) {
            <div class="chart-item">
              <div class="chart-bar">
                <div 
                  class="chart-fill" 
                  [style.width.%]="getPercentage(item.value, getTotalEvents())"
                  [style.background-color]="item.color">
                </div>
              </div>
              <div class="chart-label">
                <span class="label-color" [style.background-color]="item.color"></span>
                <span class="label-text">{{ item.name }}</span>
                <span class="label-value">{{ item.value }} ({{ getPercentage(item.value, getTotalEvents()) }}%)</span>
              </div>
            </div>
          }
        </div>
      </div>
    </nz-card>

    <!-- Évolution mensuelle -->
    <nz-card class="chart-card" [nzLoading]="isLoading">
      <div class="chart-header">
        <h3 class="chart-title">Évolution mensuelle</h3>
        <p class="chart-subtitle">Événements et chiffre d'affaires</p>
      </div>
      <div class="chart-content">
        <div class="line-chart-simulation">
          @for (month of monthlyData; track month.month) {
            <div class="month-data">
              <div class="month-bars">
                <div class="events-bar">
                  <div 
                    class="bar-fill events" 
                    [style.height.%]="(month.events / getMaxValue(monthlyData, 'events')) * 100">
                  </div>
                </div>
                <div class="revenue-bar">
                  <div 
                    class="bar-fill revenue" 
                    [style.height.%]="(month.revenue / getMaxValue(monthlyData, 'revenue')) * 100">
                  </div>
                </div>
              </div>
              <div class="month-label">{{ month.month }}</div>
              <div class="month-values">
                <div class="events-value">{{ month.events }} evt</div>
                <div class="revenue-value">{{ formatCurrency(month.revenue) }}</div>
              </div>
            </div>
          }
        </div>
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color events"></span>
            <span>Événements</span>
          </div>
          <div class="legend-item">
            <span class="legend-color revenue"></span>
            <span>Chiffre d'affaires</span>
          </div>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Insights et recommandations -->
  <nz-card class="insights-card">
    <div class="insights-header">
      <h3 class="insights-title">📊 Insights et recommandations</h3>
    </div>
    <div class="insights-grid">
      <div class="insight-item success">
        <div class="insight-icon">🎯</div>
        <div class="insight-content">
          <h4>Performance excellente</h4>
          <p>Votre taux de satisfaction client de {{ advancedStats.customerSatisfaction }}% est exceptionnel !</p>
        </div>
      </div>
      <div class="insight-item info">
        <div class="insight-icon">📈</div>
        <div class="insight-content">
          <h4>Croissance soutenue</h4>
          <p>Croissance de {{ advancedStats.growthRate }}% par rapport à la période précédente.</p>
        </div>
      </div>
      <div class="insight-item warning">
        <div class="insight-icon">💡</div>
        <div class="insight-content">
          <h4>Opportunité</h4>
          <p>{{ advancedStats.topEventType }} représente votre segment le plus rentable.</p>
        </div>
      </div>
    </div>
  </nz-card>
</div>
