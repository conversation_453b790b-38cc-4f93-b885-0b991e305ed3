:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  height: 100vh;
  background: var(--apple-gray-50);
}

.menu-sidebar {
  position: fixed !important;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  height: 100vh !important;
  overflow-y: auto;
  box-shadow: 2px 0 6px rgba(0,21,41,.35);
  background: var(--apple-grenat-soft) !important;
}

.header-trigger {
  height: 64px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  color: var(--apple-gray-600);
}

.trigger:hover {
  color: var(--apple-black);
  transform: scale(1.05);
}

.sidebar-logo {
  position: relative;
  height: 80px;
  padding: 16px 24px;
  overflow: hidden;
  background: var(--apple-grenat-soft);
  transition: all .3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-logo a {
  display: flex;
  align-items: center;
  justify-content: center; /* Centrer le logo */
  text-decoration: none;
  width: 100%;
}

/* Styles spécifiques pour le logo selon l'état de la sidebar */
.ant-layout-sider-collapsed .sidebar-logo {
  height: 64px;
  padding: 16px 8px;
  background: var(--apple-grenat-soft) !important;
}

/* Pas de changement de direction nécessaire */

.ant-layout-sider-collapsed .sidebar-logo img {
  height: 32px;
  max-width: 32px;
}

.ant-layout-sider:not(.ant-layout-sider-collapsed) .sidebar-logo img {
  height: 48px; /* Logo plus grand quand ouvert */
  max-width: 150px;
}

.sidebar-logo img {
  display: block;
  width: auto;
  transition: all .3s;
  object-fit: contain; /* Maintenir les proportions */
  margin: 0 auto; /* Centrer le logo */
}

/* Styles h1 supprimés car plus de texte */

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

/* Layout principal qui s'adapte à la sidebar fixe */
.main-content {
  margin-left: 256px; /* Largeur de la sidebar ouverte */
  transition: margin-left 0.3s ease;
  min-height: 100vh;
}

.main-content.collapsed {
  margin-left: 80px; /* Largeur de la sidebar fermée */
}

nz-content {
  margin: 0;
  background: #fafafa;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
}

.inner-content {
  padding: 0;
  background: #fafafa;
  min-height: calc(100vh - 64px);
}

/* Styles spécifiques pour le menu sidebar - éviter le blanc */
.menu-sidebar .ant-menu-light {
  background: var(--apple-grenat-soft) !important;
}

/* Sidebar collapsed - background grenat-soft aussi */
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-light {
  background: var(--apple-grenat-soft) !important;
}

.menu-sidebar .ant-menu-light .ant-menu-item,
.menu-sidebar .ant-menu-light .ant-menu-submenu-title {
  color: var(--apple-black) !important;
  background: transparent !important;
}

/* Forcer spécifiquement les titres de submenu à être noirs */
.menu-sidebar .ant-menu-light .ant-menu-submenu-title,
.menu-sidebar .ant-menu-light .ant-menu-submenu-title span,
.menu-sidebar .ant-menu-light .ant-menu-submenu-title .ant-menu-submenu-title-content {
  color: var(--apple-black) !important;
}

.menu-sidebar .ant-menu-light .ant-menu-item:hover,
.menu-sidebar .ant-menu-light .ant-menu-submenu-title:hover {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

/* Forcer les liens et spans à ne jamais être blancs en hover */
.menu-sidebar .ant-menu-light .ant-menu-item:hover a,
.menu-sidebar .ant-menu-light .ant-menu-item:hover span,
.menu-sidebar .ant-menu-light .ant-menu-submenu-title:hover a,
.menu-sidebar .ant-menu-light .ant-menu-submenu-title:hover span,
.menu-sidebar .ant-menu-light .ant-menu-item:hover .anticon,
.menu-sidebar .ant-menu-light .ant-menu-submenu-title:hover .anticon {
  color: var(--apple-grenat) !important;
}

/* Forcer les ul à ne jamais être blancs */
.menu-sidebar .ant-menu-light ul,
.menu-sidebar .ant-menu-light ul li,
.menu-sidebar .ant-menu-light ul li a,
.menu-sidebar .ant-menu-light ul li span {
  color: var(--apple-black) !important;
  background: transparent !important;
}

.menu-sidebar .ant-menu-light ul li:hover,
.menu-sidebar .ant-menu-light ul li:hover a,
.menu-sidebar .ant-menu-light ul li:hover span {
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-pale) !important;
}

.menu-sidebar .ant-menu-light .ant-menu-item-selected {
  background: var(--apple-grenat) !important;
  color: var(--apple-white) !important;
}

.menu-sidebar .ant-menu-light .ant-menu-item-selected a,
.menu-sidebar .ant-menu-light .ant-menu-item-selected span {
  color: var(--apple-white) !important;
}

.menu-sidebar .ant-menu-light .ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--apple-grenat) !important;
  background: transparent !important;
}

/* Forcer les titres de submenu ouverts à être grenat */
.menu-sidebar .ant-menu-light .ant-menu-submenu-open > .ant-menu-submenu-title,
.menu-sidebar .ant-menu-light .ant-menu-submenu-open > .ant-menu-submenu-title span,
.menu-sidebar .ant-menu-light .ant-menu-submenu-open > .ant-menu-submenu-title .ant-menu-submenu-title-content,
.menu-sidebar .ant-menu-light .ant-menu-submenu-open > .ant-menu-submenu-title .anticon {
  color: var(--apple-grenat) !important;
}

/* Forcer les icônes des submenu à être noires par défaut */
.menu-sidebar .ant-menu-light .ant-menu-submenu-title .anticon {
  color: var(--apple-black) !important;
}

/* Forcer les icônes des submenu en hover à être grenat */
.menu-sidebar .ant-menu-light .ant-menu-submenu-title:hover .anticon {
  color: var(--apple-grenat) !important;
}

/* Forcer spécifiquement les li nz-submenu en hover à être grenat foncé */
.menu-sidebar .ant-menu-light li[nz-submenu]:hover .ant-menu-submenu-title,
.menu-sidebar .ant-menu-light li[nz-submenu]:hover .ant-menu-submenu-title span,
.menu-sidebar .ant-menu-light li[nz-submenu]:hover .ant-menu-submenu-title .ant-menu-submenu-title-content,
.menu-sidebar .ant-menu-light li[nz-submenu]:hover .ant-menu-submenu-title .anticon {
  color: var(--apple-grenat-dark) !important;
}

/* Même chose quand le sidebar est collapsed */
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-light li[nz-submenu]:hover .ant-menu-submenu-title,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-light li[nz-submenu]:hover .ant-menu-submenu-title span,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-light li[nz-submenu]:hover .ant-menu-submenu-title .ant-menu-submenu-title-content,
.menu-sidebar.ant-layout-sider-collapsed .ant-menu-light li[nz-submenu]:hover .ant-menu-submenu-title .anticon {
  color: var(--apple-grenat-dark) !important;
}

/* Responsive pour mobile */
@media (max-width: 768px) {
  .menu-sidebar {
    position: fixed !important;
    left: -256px; /* Caché par défaut sur mobile */
    transition: left 0.3s ease;
  }

  .menu-sidebar.ant-layout-sider-collapsed {
    left: -80px;
  }

  .main-content {
    margin-left: 0 !important;
  }

  /* Overlay pour mobile quand sidebar ouverte */
  .main-content::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    z-index: 5;
    display: none;
  }
}
