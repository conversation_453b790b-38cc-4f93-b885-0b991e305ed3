.services-config-container {
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.save-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3) !important;
}

.config-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

/* Description */
.config-description {
  margin-bottom: 24px;
  padding: 16px 20px;
  background: var(--apple-gray-50);
  border-radius: 8px;
  border: 1px solid var(--apple-gray-200);
}

.config-description p {
  margin: 0;
  color: var(--apple-gray-700);
  line-height: 1.5;
}

/* Tableau de configuration */
.config-table-container {
  margin-bottom: 24px;
  overflow-x: auto;
}

.config-table {
  min-width: 800px;
}

:host ::ng-deep .config-table .ant-table-thead > tr > th {
  background: var(--apple-gray-50) !important;
  border-bottom: 2px solid var(--apple-gray-200) !important;
  color: var(--apple-grenat) !important;
  font-weight: 600 !important;
  padding: 12px 8px !important;
  text-align: center !important;
}

:host ::ng-deep .config-table .ant-table-tbody > tr > td {
  padding: 12px 8px !important;
  border-bottom: 1px solid var(--apple-gray-100) !important;
  text-align: center !important;
  vertical-align: middle !important;
}

:host ::ng-deep .config-table .ant-table-tbody > tr:hover > td {
  background: var(--apple-grenat-soft) !important;
}

/* Headers du tableau */
.service-header .header-content,
.event-type-header .header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.event-type-name {
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  word-break: break-word;
}

.header-actions {
  display: flex;
  gap: 4px;
}

.header-actions button {
  color: var(--apple-gray-400) !important;
  transition: all 0.2s ease !important;
}

.header-actions button:hover {
  color: var(--apple-grenat) !important;
}

/* Cellule de service */
.service-name {
  text-align: left !important;
  padding: 16px 12px !important;
}

.service-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-title {
  font-weight: 500;
  color: var(--apple-gray-800);
}

.service-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.service-info:hover .service-actions {
  opacity: 1;
}

.service-actions button {
  color: var(--apple-gray-400) !important;
  transition: all 0.2s ease !important;
}

.service-actions button:hover {
  color: var(--apple-grenat) !important;
}

/* Cellules de switch */
.switch-cell {
  padding: 8px !important;
}

:host ::ng-deep .ant-switch-checked {
  background-color: var(--apple-grenat) !important;
}

:host ::ng-deep .ant-switch:hover:not(.ant-switch-disabled) {
  background-color: var(--apple-grenat-pale) !important;
}

/* Résumé */
.config-summary {
  padding: 16px 20px;
  background: var(--apple-gray-50);
  border-radius: 8px;
  border: 1px solid var(--apple-gray-200);
}

.summary-stats {
  display: flex;
  gap: 32px;
  justify-content: center;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--apple-gray-600);
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--apple-grenat);
}

/* Responsive */
@media (max-width: 1200px) {
  .event-type-name {
    font-size: 11px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 2px;
  }
}

@media (max-width: 768px) {
  .services-config-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .service-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .service-actions {
    opacity: 1;
  }
}
