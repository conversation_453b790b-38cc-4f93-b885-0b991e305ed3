.dashboard-container {
  padding: 24px 32px 16px 32px;
  background: var(--apple-gray-50);
  min-height: 100vh;
}

/* Header */
.dashboard-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.welcome-section {
  flex: 1;
}

.dashboard-title {
  margin: 0 0 8px 0;
  font-size: 36px;
  font-weight: 800;
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.dashboard-subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--apple-gray-600);
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  border-radius: 8px !important;
  border: 1px solid var(--apple-gray-300) !important;
  color: var(--apple-gray-700) !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.action-btn:hover {
  border-color: var(--apple-grenat) !important;
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-soft) !important;
}

/* Grille des statistiques */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 16px;
  border: 1px solid var(--apple-gray-200);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 8px;
}

.stat-icon {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  flex-shrink: 0;
}

.revenue-icon {
  background: linear-gradient(135deg, #8B1538, #a91d42);
}

.events-icon {
  background: linear-gradient(135deg, #34c759, #30d158);
}

.clients-icon {
  background: linear-gradient(135deg, #007aff, #0056cc);
}

.completion-icon {
  background: linear-gradient(135deg, #ff9500, #ff8c00);
}

.stat-info {
  flex: 1;
  min-width: 0;
}

:host ::ng-deep .stat-number .ant-statistic-title {
  color: var(--apple-gray-600) !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
}

:host ::ng-deep .stat-number .ant-statistic-content {
  color: var(--apple-gray-900) !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  line-height: 1 !important;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  color: #34c759;
}

.completion-progress {
  margin-top: 8px;
}

/* Contenu principal */
.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

/* Actions rapides */
.quick-actions-card {
  border-radius: 16px;
  border: 1px solid var(--apple-gray-200);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.card-title {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.card-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
}

.view-all-btn {
  color: var(--apple-grenat) !important;
  font-weight: 500 !important;
  padding: 0 !important;
}

.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid var(--apple-gray-200);
  border-radius: 12px;
  border-left: 4px solid;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--apple-white);
}

.quick-action-item:hover {
  background: var(--apple-gray-50);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(139, 21, 56, 0.1);
  border-radius: 12px;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
  min-width: 0;
}

.action-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.action-description {
  margin: 0;
  font-size: 14px;
  color: var(--apple-gray-600);
}

.action-arrow {
  color: var(--apple-gray-400);
  transition: all 0.2s ease;
}

.quick-action-item:hover .action-arrow {
  color: var(--apple-grenat);
  transform: translateX(4px);
}

/* Événements récents */
.recent-events-card {
  border-radius: 16px;
  border: 1px solid var(--apple-gray-200);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid var(--apple-gray-200);
  border-radius: 12px;
  background: var(--apple-white);
  transition: all 0.2s ease;
}

.event-item:hover {
  background: var(--apple-gray-50);
  border-color: var(--apple-grenat-soft);
}

.event-avatar {
  flex-shrink: 0;
}

.event-info {
  flex: 1;
  min-width: 0;
}

.event-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--apple-gray-900);
}

.event-client {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--apple-gray-600);
}

.event-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--apple-gray-500);
}

.event-status {
  flex-shrink: 0;
}

.event-revenue {
  flex-shrink: 0;
  text-align: right;
}

.revenue-amount {
  font-size: 16px;
  font-weight: 600;
  color: var(--apple-grenat);
}

/* Responsive */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-title {
    font-size: 28px;
  }
}
