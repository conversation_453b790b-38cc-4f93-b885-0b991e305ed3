<div class="inventory-container">
  <!-- Header avec titre et actions -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">Inventaire du matériel de location</h2>
      <div class="header-actions">
        <button 
          nz-button 
          nzType="default" 
          (click)="resetInventory()"
          [nzLoading]="isLoading"
          nz-tooltip="Réinitialiser l'inventaire">
          <nz-icon nzType="reload"></nz-icon>
          Réinitialiser
        </button>
        <button 
          nz-button 
          nzType="dashed" 
          (click)="addNewMaterial()"
          class="add-btn">
          <nz-icon nzType="plus"></nz-icon>
          Ajouter matériel
        </button>
        <button 
          nz-button 
          nzType="primary" 
          (click)="saveInventory()"
          [nzLoading]="isLoading"
          class="save-btn">
          <nz-icon nzType="save"></nz-icon>
          <PERSON><PERSON><PERSON><PERSON>
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section">
    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon types-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H4.99C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 15H15C15 16.66 13.65 18 12 18C10.35 18 9 16.66 9 15H5V5H19V15Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ materials.length }}</div>
          <div class="stat-label">Types de matériel</div>
        </div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon total-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM9 17H7V10H9V17ZM13 17H11V7H13V17ZM17 17H15V13H17V17Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ getTotalItems() }}</div>
          <div class="stat-label">Articles au total</div>
        </div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-content">
        <div class="stat-icon categories-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2ZM4 14L5 17H7L6 14H4ZM9.5 12.5L11 16H13L11.5 12.5H9.5ZM17 14L16 17H18L19 14H17Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ getCategoriesCount() }}</div>
          <div class="stat-label">Catégories</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Grille des matériaux -->
  <div class="materials-grid">
    @for (material of materials; track material.id) {
      <div class="material-card" [class.loading]="isLoading">
        <div class="material-header">
          <div class="material-icon" [class]="'icon-' + material.id">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              @if (material.id === '1') {
                <!-- Chapiteaux -->
                <path d="M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
              } @else if (material.id === '2') {
                <!-- Estrade -->
                <path d="M3 20H21V18H20V16H19V14H18V12H17V10H16V8H15V6H14V4H13V2H11V4H10V6H9V8H8V10H7V12H6V14H5V16H4V18H3V20Z" fill="currentColor"/>
              } @else if (material.id === '3') {
                <!-- Dalo -->
                <path d="M6 2H18C19.1 2 20 2.9 20 4V20C20 21.1 19.1 22 18 22H6C4.9 22 4 21.1 4 20V4C4 2.9 4.9 2 6 2ZM6 4V8H18V4H6ZM6 10V14H18V10H6ZM6 16V20H18V16H6Z" fill="currentColor"/>
              } @else if (material.id === '4') {
                <!-- Scène -->
                <path d="M4 6H20V8H4V6ZM6 10H18V12H6V10ZM8 14H16V16H8V14ZM10 18H14V20H10V18Z" fill="currentColor"/>
              } @else if (material.id === '5') {
                <!-- Salle de fête -->
                <path d="M12 2L2 7V17H22V7L12 2ZM4 15V9L12 5L20 9V15H4Z" fill="currentColor"/>
              } @else if (material.id === '6') {
                <!-- Tables rondes -->
                <path d="M12 2C17.52 2 22 6.48 22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2ZM12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12C20 7.58 16.42 4 12 4Z" fill="currentColor"/>
              } @else if (material.id === '7') {
                <!-- Chaises -->
                <path d="M7 11V7C7 5.9 7.9 5 9 5H15C16.1 5 17 5.9 17 7V11H19V7C19 4.79 17.21 3 15 3H9C6.79 3 5 4.79 5 7V11H7ZM5 13V21H7V19H17V21H19V13H5Z" fill="currentColor"/>
              } @else if (material.id === '8') {
                <!-- Éclairage LED -->
                <path d="M9 21C6.24 21 4 18.76 4 16H6C6 17.66 7.34 19 9 19S12 17.66 12 16H14C14 18.76 11.76 21 9 21ZM9 2L6.05 5.05L7.46 6.46L9 4.92L10.54 6.46L11.95 5.05L9 2ZM20 10.5V12.5H17V10.5H20ZM2 10.5V12.5H5V10.5H2ZM15.54 6.46L16.95 5.05L14 2L11.05 5.05L12.46 6.46L14 4.92L15.54 6.46Z" fill="currentColor"/>
              } @else {
                <!-- Icône par défaut -->
                <path d="M19 3H4.99C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 15H15C15 16.66 13.65 18 12 18C10.35 18 9 16.66 9 15H5V5H19V15Z" fill="currentColor"/>
              }
            </svg>
          </div>
          <div class="material-info">
            <h3 class="material-name">{{ material.name }}</h3>
            <p class="material-description">{{ material.description }}</p>
            <span class="material-category">{{ material.category }}</span>
          </div>
          <div class="material-actions">
            <button 
              nz-button 
              nzType="text" 
              nzDanger
              nzSize="small"
              (click)="deleteMaterial(material)"
              nz-tooltip="Supprimer ce matériel"
              class="delete-btn">
              <nz-icon nzType="delete"></nz-icon>
            </button>
          </div>
        </div>
        
        <div class="quantity-section">
          <div class="quantity-label">
            <span>Quantité disponible</span>
            <span class="unit-label">{{ material.unit }}</span>
          </div>
          <div class="quantity-controls">
            <nz-input-number
              [(ngModel)]="material.quantity"
              [nzMin]="0"
              [nzMax]="9999"
              [nzStep]="1"
              (ngModelChange)="updateQuantity(material, $event)"
              class="quantity-input">
            </nz-input-number>
          </div>
        </div>
        
        <div class="availability-indicator">
          @if (material.quantity === 0) {
            <div class="status-badge unavailable">
              <nz-icon nzType="close-circle"></nz-icon>
              Non disponible
            </div>
          } @else if (material.quantity <= 2) {
            <div class="status-badge low-stock">
              <nz-icon nzType="exclamation-circle"></nz-icon>
              Stock faible
            </div>
          } @else {
            <div class="status-badge available">
              <nz-icon nzType="check-circle"></nz-icon>
              Disponible
            </div>
          }
        </div>
      </div>
    }
  </div>

  <!-- Message si aucun matériel -->
  @if (materials.length === 0) {
    <div class="empty-state">
      <div class="empty-content">
        <div class="empty-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H4.99C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 15H15C15 16.66 13.65 18 12 18C10.35 18 9 16.66 9 15H5V5H19V15Z" fill="currentColor"/>
          </svg>
        </div>
        <h3>Aucun matériel dans l'inventaire</h3>
        <p>Commencez par ajouter du matériel de location à votre inventaire.</p>
        <button
          nz-button
          nzType="primary"
          (click)="addNewMaterial()"
          class="add-first-btn">
          <nz-icon nzType="plus"></nz-icon>
          Ajouter le premier matériel
        </button>
      </div>
    </div>
  }
</div>
