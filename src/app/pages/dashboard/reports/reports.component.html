<div class="reports-container">
  <!-- Header avec filtres -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h2 class="page-title">Rapports et analyses</h2>
        <p class="page-subtitle">Générez et consultez vos rapports d'activité</p>
      </div>
      <div class="header-filters">
        <nz-range-picker 
          [(ngModel)]="dateRange"
          nzFormat="dd/MM/yyyy"
          class="date-range-picker">
        </nz-range-picker>
        <nz-select 
          [(ngModel)]="selectedPeriod"
          class="period-select">
          <nz-option nzValue="week" nzLabel="Cette semaine"></nz-option>
          <nz-option nzValue="month" nzLabel="Ce mois"></nz-option>
          <nz-option nzValue="quarter" nzLabel="Ce trimestre"></nz-option>
          <nz-option nzValue="year" nzLabel="Cette année"></nz-option>
        </nz-select>
      </div>
    </div>
  </div>

  <!-- Templates de rapports -->
  <div class="reports-section">
    <div class="section-header">
      <h3 class="section-title">📊 Templates de rapports</h3>
      <p class="section-subtitle">Choisissez le type de rapport à générer</p>
    </div>
    
    <div class="templates-grid">
      @for (template of reportTemplates; track template.id) {
        <nz-card class="template-card" [nzLoading]="isGenerating">
          <div class="template-content">
            <div class="template-header">
              <div class="template-icon" [style.background-color]="template.color">
                <nz-icon [nzType]="template.icon"></nz-icon>
              </div>
              <div class="template-info">
                <h4 class="template-name">{{ template.name }}</h4>
                <p class="template-description">{{ template.description }}</p>
                <div class="template-meta">
                  <span class="template-category">{{ template.category }}</span>
                  <span class="template-last-generated">
                    {{ getLastGeneratedText(template.lastGenerated) }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="template-actions">
              <button 
                nz-button 
                nzType="default" 
                nzSize="small"
                (click)="scheduleReport(template)"
                class="schedule-btn">
                <nz-icon nzType="clock-circle"></nz-icon>
                Programmer
              </button>
              <button 
                nz-button 
                nzType="primary" 
                (click)="generateReport(template)"
                [nzLoading]="isGenerating"
                class="generate-btn">
                <nz-icon nzType="file-add"></nz-icon>
                Générer
              </button>
            </div>
          </div>
        </nz-card>
      }
    </div>
  </div>

  <!-- Rapports générés -->
  <div class="generated-section">
    <div class="section-header">
      <h3 class="section-title">📁 Rapports générés</h3>
      <p class="section-subtitle">Historique de vos rapports récents</p>
    </div>

    @if (generatedReports.length > 0) {
      <nz-card class="reports-table-card">
        <nz-table 
          [nzData]="generatedReports" 
          nzSize="middle"
          [nzShowPagination]="false">
          <thead>
            <tr>
              <th>Nom du rapport</th>
              <th>Type</th>
              <th>Date de génération</th>
              <th>Taille</th>
              <th>Statut</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            @for (report of generatedReports; track report.id) {
              <tr>
                <td>
                  <div class="report-name">
                    <nz-icon nzType="file-pdf" class="file-icon"></nz-icon>
                    {{ report.name }}
                  </div>
                </td>
                <td>
                  <span class="report-type">{{ report.type }}</span>
                </td>
                <td>{{ formatDate(report.generatedAt) }}</td>
                <td>{{ report.size }}</td>
                <td>
                  <nz-tag [nzColor]="getStatusColor(report.status)">
                    {{ getStatusText(report.status) }}
                  </nz-tag>
                  @if (report.status === 'generating') {
                    <nz-progress 
                      nzType="line" 
                      [nzPercent]="65" 
                      nzSize="small"
                      class="progress-bar">
                    </nz-progress>
                  }
                </td>
                <td>
                  <div class="report-actions">
                    @if (report.status === 'ready') {
                      <button 
                        nz-button 
                        nzType="text" 
                        nzSize="small"
                        (click)="downloadReport(report)"
                        nz-tooltip="Télécharger">
                        <nz-icon nzType="download"></nz-icon>
                      </button>
                    }
                    <button 
                      nz-button 
                      nzType="text" 
                      nzDanger
                      nzSize="small"
                      (click)="deleteReport(report.id)"
                      nz-tooltip="Supprimer">
                      <nz-icon nzType="delete"></nz-icon>
                    </button>
                  </div>
                </td>
              </tr>
            }
          </tbody>
        </nz-table>
      </nz-card>
    } @else {
      <nz-card class="empty-reports">
        <div class="empty-content">
          <div class="empty-icon">📄</div>
          <h4>Aucun rapport généré</h4>
          <p>Commencez par générer votre premier rapport en utilisant les templates ci-dessus.</p>
        </div>
      </nz-card>
    }
  </div>

  <!-- Conseils et informations -->
  <nz-card class="tips-card">
    <div class="tips-header">
      <h3 class="tips-title">💡 Conseils pour vos rapports</h3>
    </div>
    <div class="tips-grid">
      <div class="tip-item">
        <div class="tip-icon">⏰</div>
        <div class="tip-content">
          <h4>Programmation automatique</h4>
          <p>Programmez vos rapports pour qu'ils soient générés automatiquement chaque mois.</p>
        </div>
      </div>
      <div class="tip-item">
        <div class="tip-icon">📊</div>
        <div class="tip-content">
          <h4>Analyse comparative</h4>
          <p>Comparez vos performances sur différentes périodes pour identifier les tendances.</p>
        </div>
      </div>
      <div class="tip-item">
        <div class="tip-icon">📧</div>
        <div class="tip-content">
          <h4>Partage facilité</h4>
          <p>Exportez vos rapports en PDF ou Excel pour les partager avec votre équipe.</p>
        </div>
      </div>
    </div>
  </nz-card>
</div>
