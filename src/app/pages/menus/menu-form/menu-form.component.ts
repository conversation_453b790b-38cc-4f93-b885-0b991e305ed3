import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

// Ng-Zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';

interface MenuItem {
  name: string;
  checked: boolean;
}

@Component({
  selector: 'app-menu-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzCardModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzCheckboxModule,
    NzSpinModule
  ],
  templateUrl: './menu-form.component.html',
  styleUrl: './menu-form.component.css'
})
export class MenuFormComponent implements OnInit {
  menuForm!: FormGroup;
  isEditMode = false;
  menuId: string | null = null;
  isSubmitting = false;
  isFormInitialized = false;



  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.checkEditMode();

    // S'assurer que le formulaire est complètement initialisé
    setTimeout(() => {
      this.isFormInitialized = true;
    }, 0);
  }

  initForm(): void {
    this.menuForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      items: this.fb.array([])
    });

    // Ajouter un seul élément par défaut
    this.addMenuItem('');
  }

  get items(): FormArray {
    return this.menuForm?.get('items') as FormArray;
  }

  isValidIndex(index: number): boolean {
    return this.items && index >= 0 && index < this.items.length;
  }

  checkEditMode(): void {
    this.menuId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.menuId;

    if (this.isEditMode) {
      this.loadMenuData();
    }
  }

  loadMenuData(): void {
    // Simulation de chargement des données
    if (this.menuId === '1') {
      this.menuForm.patchValue({
        title: 'Menu Sultana'
      });

      // Charger les éléments du menu
      this.clearItems();
      const menuItems = [
        'Cocktail de bienvenue',
        'Amuse bouche: Macaron, amande, nougat et fekkas',
        'Jus avec gâteaux soirée',
        'Sushi',
        'Briouate et mini cigare au fromage',
        'Pastilla de fruits de mer',
        'Mechoui d\'agneau garni',
        'Fruits de saison',
        'Soda et eau minérale',
        'Thé avec des gâteaux Marocains',
        'Tarte glacée',
        'Centre de table'
      ];

      menuItems.forEach(item => this.addMenuItem(item));
    }
  }

  addMenuItem(name: string = ''): void {
    const itemGroup = this.fb.group({
      name: [name, Validators.required]
    });
    this.items.push(itemGroup);
    // Forcer la détection de changement
    this.items.updateValueAndValidity();
  }

  removeMenuItem(index: number): void {
    if (this.isValidIndex(index) && this.items.length > 1) {
      this.items.removeAt(index);
      this.items.updateValueAndValidity();
    }
  }

  clearItems(): void {
    if (this.items) {
      while (this.items.length !== 0) {
        this.items.removeAt(0);
      }
      this.items.updateValueAndValidity();
    }
  }

  onSubmit(): void {
    if (this.menuForm.valid) {
      this.isSubmitting = true;

      const formData = this.menuForm.value;
      console.log('Données du menu:', formData);

      // Simulation de sauvegarde
      setTimeout(() => {
        this.isSubmitting = false;
        this.message.success(
          this.isEditMode ? 'Menu modifié avec succès !' : 'Menu créé avec succès !'
        );
        this.router.navigate(['/menus']);
      }, 1000);
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.router.navigate(['/menus']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.menuForm.controls).forEach(key => {
      const control = this.menuForm.get(key);
      control?.markAsTouched();
    });

    this.items.controls.forEach(control => {
      Object.keys(control.value).forEach(key => {
        control.get(key)?.markAsTouched();
      });
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.menuForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName} est requis`;
      }
      if (field.errors['minlength']) {
        return `${fieldName} doit contenir au moins ${field.errors['minlength'].requiredLength} caractères`;
      }
      if (field.errors['min']) {
        return `${fieldName} doit être supérieur ou égal à ${field.errors['min'].min}`;
      }
    }
    return '';
  }

  getItemError(index: number, fieldName: string): string {
    if (!this.isValidIndex(index)) {
      return '';
    }
    try {
      const item = this.items.at(index);
      const field = item?.get(fieldName);
      if (field?.errors && field.touched) {
        if (field.errors['required']) {
          return 'Nom de l\'élément requis';
        }
      }
    } catch (error) {
      console.warn('Error getting item error:', error);
    }
    return '';
  }
}
