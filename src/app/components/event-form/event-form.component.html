<div class="event-form-container">
  <!-- Header avec titre -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">{{ isEditMode ? 'Modification de l\'événement' : 'Création d\'un nouvel événement' }}</h2>
    </div>
  </div>

  <!-- Steps Navigation -->
  <nz-steps [nzCurrent]="currentStep()" nzSize="small" class="steps-nav">
    <nz-step nzTitle="Type d'événement"></nz-step>
    <nz-step nzTitle="Client"></nz-step>
    <nz-step nzTitle="Détails"></nz-step>
    <nz-step nzTitle="Paiement"></nz-step>
    <nz-step nzTitle="Finalisation"></nz-step>
  </nz-steps>

  <nz-card class="form-card">

    <!-- Étape 1: Type d'événement -->
    @if (currentStep() === 0) {
      <div class="step-content">
      <h3>Choisissez le type d'événement</h3>

      <form [formGroup]="step1Form" nz-form nzLayout="vertical">
        <!-- Choix Particulier/Entreprise -->
        <nz-form-item>
          <nz-form-label nzRequired>Catégorie d'événement</nz-form-label>
          <nz-form-control nzErrorTip="Veuillez choisir une catégorie">
            <nz-radio-group formControlName="category" (ngModelChange)="onCategoryChange()" class="category-choice">
              <div class="category-cards">
                <div class="category-card" [class.selected]="step1Form.get('category')?.value === 'particulier'" (click)="selectCategory('particulier')">
                  <input type="radio" name="category" value="particulier" style="display: none;">
                  <div class="card-content">
                    <nz-icon nzType="user" nzTheme="outline" class="category-icon"></nz-icon>
                    <h4>Particulier</h4>
                    <p>Événements privés et familiaux</p>
                  </div>
                </div>
                <div class="category-card" [class.selected]="step1Form.get('category')?.value === 'entreprise'" (click)="selectCategory('entreprise')">
                  <input type="radio" name="category" value="entreprise" style="display: none;">
                  <div class="card-content">
                    <nz-icon nzType="bank" nzTheme="outline" class="category-icon"></nz-icon>
                    <h4>Entreprise</h4>
                    <p>Événements professionnels</p>
                  </div>
                </div>
                <div class="category-card" [class.selected]="step1Form.get('category')?.value === 'evenements-speciaux'" (click)="selectCategory('evenements-speciaux')">
                  <input type="radio" name="category" value="evenements-speciaux" style="display: none;">
                  <div class="card-content">
                    <nz-icon nzType="star" nzTheme="outline" class="category-icon"></nz-icon>
                    <h4>Événements spéciaux</h4>
                    <p>Cinéma, cérémonie</p>
                  </div>
                </div>
                <div class="category-card" [class.selected]="step1Form.get('category')?.value === 'location-materiel'" (click)="selectCategory('location-materiel')">
                  <input type="radio" name="category" value="location-materiel" style="display: none;">
                  <div class="card-content">
                    <nz-icon nzType="tool" nzTheme="outline" class="category-icon"></nz-icon>
                    <h4>Location de matériel</h4>
                    <p>Chapiteaux, estrade, dalo, scène</p>
                  </div>
                </div>
              </div>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>

        <!-- Type d'événement spécifique -->
        @if (step1Form.get('category')?.value) {
          <nz-form-item>
            <nz-form-label nzRequired>Type d'événement</nz-form-label>
            <nz-form-control [nzErrorTip]="getStep1FieldError('eventType')">
              <nz-select formControlName="eventType" nzPlaceHolder="Sélectionnez le type d'événement">
                @for (type of filteredEventTypes; track type.value) {
                  <nz-option
                    [nzValue]="type.value"
                    [nzLabel]="type.label">
                  </nz-option>
                }
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        }
      </form>
      </div>
    }

    <!-- Étape 2: Client -->
    @if (currentStep() === 1) {
      <div class="step-content">
      <h3>Informations client</h3>

      <form [formGroup]="step2Form" nz-form nzLayout="vertical">
        <nz-form-item>
          <nz-form-label>Type de client</nz-form-label>
          <nz-form-control>
            <nz-radio-group formControlName="clientType" (ngModelChange)="onClientTypeChange()">
              <label nz-radio nzValue="existing">Client existant</label>
              <label nz-radio nzValue="new">Nouveau client</label>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>

        <!-- Client existant -->
        @if (!isClientTypeNew) {
          <nz-form-item>
            <nz-form-label nzRequired>Sélectionner un client</nz-form-label>
            <nz-form-control [nzErrorTip]="getStep2FieldError('selectedClient')">
              <nz-select formControlName="selectedClient" nzPlaceHolder="Choisir un client">
                @for (client of existingClients; track client.id) {
                  <nz-option
                    [nzValue]="client.id"
                    [nzLabel]="client.nom + ' ' + client.prenom">
                  </nz-option>
                }
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        }

        <!-- Formulaire nouveau client inline -->
        @if (isClientTypeNew) {
          <div formGroupName="newClient">
          <nz-form-item>
            <nz-form-label nzRequired>Nom</nz-form-label>
            <nz-form-control [nzErrorTip]="getNewClientFieldError('nom')">
              <input nz-input formControlName="nom" placeholder="Nom du client" />
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-label nzRequired>Prénom</nz-form-label>
            <nz-form-control [nzErrorTip]="getNewClientFieldError('prenom')">
              <input nz-input formControlName="prenom" placeholder="Prénom du client" />
            </nz-form-control>
          </nz-form-item>

          <div class="form-row">
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>CIN</nz-form-label>
                <nz-form-control [nzErrorTip]="getNewClientFieldError('cin')">
                  <input nz-input formControlName="cin" placeholder="CIN" />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>Ville</nz-form-label>
                <nz-form-control [nzErrorTip]="getNewClientFieldError('ville')">
                  <input nz-input formControlName="ville" placeholder="Ville" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item>
            <nz-form-label nzRequired>Adresse</nz-form-label>
            <nz-form-control [nzErrorTip]="getNewClientFieldError('adresse')">
              <textarea nz-input formControlName="adresse" placeholder="Adresse complète" rows="2"></textarea>
            </nz-form-control>
          </nz-form-item>

          <div class="form-row">
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>Téléphone 1</nz-form-label>
                <nz-form-control [nzErrorTip]="getNewClientFieldError('telephone1')">
                  <input nz-input formControlName="telephone1" placeholder="Téléphone principal" />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div class="form-col">
              <nz-form-item>
                <nz-form-label>Téléphone 2</nz-form-label>
                <nz-form-control [nzErrorTip]="getNewClientFieldError('telephone2')">
                  <input nz-input formControlName="telephone2" placeholder="Téléphone secondaire" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item>
            <nz-form-label nzRequired>Email</nz-form-label>
            <nz-form-control [nzErrorTip]="getNewClientFieldError('email')">
              <input nz-input formControlName="email" placeholder="Adresse email" type="email" />
            </nz-form-control>
          </nz-form-item>
          </div>
        }
      </form>
      </div>
    }

    <!-- Étape 3: Détails événement -->
    @if (currentStep() === 2) {
      <div class="step-content">
      <h3>Détails de l'événement</h3>

      <form [formGroup]="step3Form" nz-form nzLayout="vertical">
        <!-- Informations communes -->
        <div class="section">
          <h4>Informations générales</h4>

          <div class="form-row-3">
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>Nombre de personnes</nz-form-label>
                <nz-form-control nzErrorTip="Le nombre de personnes est requis">
                  <nz-input-number formControlName="nbPersonnes" [nzMin]="1" [nzMax]="10000" nzPlaceHolder="Nombre"></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>Date et heure</nz-form-label>
                <nz-form-control nzErrorTip="La date et l'heure sont requises">
                  <nz-date-picker
                    formControlName="date"
                    nzShowTime
                    nzFormat="dd/MM/yyyy HH:mm"
                    nzPlaceHolder="Sélectionner la date et l'heure">
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>Ville</nz-form-label>
                <nz-form-control nzErrorTip="La ville est requise">
                  <nz-select formControlName="ville" nzPlaceHolder="Sélectionner la ville" nzShowSearch>
                    <nz-option nzValue="Casablanca" nzLabel="Casablanca"></nz-option>
                    <nz-option nzValue="Rabat" nzLabel="Rabat"></nz-option>
                    <nz-option nzValue="Marrakech" nzLabel="Marrakech"></nz-option>
                    <nz-option nzValue="Fès" nzLabel="Fès"></nz-option>
                    <nz-option nzValue="Tanger" nzLabel="Tanger"></nz-option>
                    <nz-option nzValue="Agadir" nzLabel="Agadir"></nz-option>
                    <nz-option nzValue="Meknès" nzLabel="Meknès"></nz-option>
                    <nz-option nzValue="Oujda" nzLabel="Oujda"></nz-option>
                    <nz-option nzValue="Kenitra" nzLabel="Kenitra"></nz-option>
                    <nz-option nzValue="Tétouan" nzLabel="Tétouan"></nz-option>
                    <nz-option nzValue="Safi" nzLabel="Safi"></nz-option>
                    <nz-option nzValue="Mohammedia" nzLabel="Mohammedia"></nz-option>
                    <nz-option nzValue="El Jadida" nzLabel="El Jadida"></nz-option>
                    <nz-option nzValue="Beni Mellal" nzLabel="Beni Mellal"></nz-option>
                    <nz-option nzValue="Autre" nzLabel="Autre"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div class="form-row-2">
            <div class="form-col">
              <nz-form-item>
                <nz-form-label>Menus</nz-form-label>
                <nz-form-control>
                  <nz-select
                    formControlName="menus"
                    nzMode="multiple"
                    nzPlaceHolder="Sélectionner les menus"
                    nzAllowClear>
                    @for (menu of availableMenus; track menu.id) {
                      <nz-option [nzValue]="menu.id" [nzLabel]="menu.title"></nz-option>
                    }
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>Adresse du lieu</nz-form-label>
                <nz-form-control nzErrorTip="L'adresse est requise">
                  <textarea nz-input formControlName="adresse" placeholder="Adresse complète du lieu" rows="2"></textarea>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </div>

        <!-- Services pour tous les types d'événements -->
        <div class="section special-section">
          <h4>Services</h4>

          <!-- Packs selon le type d'événement -->
          @if (selectedEventType === 'mariage') {
            <nz-form-item>
              <nz-form-label>Packs</nz-form-label>
              <nz-form-control>
                <nz-select formControlName="packs" nzMode="multiple" nzPlaceHolder="Sélectionner les packs">
                  @for (pack of packOptions.mariage; track pack) {
                    <nz-option [nzValue]="pack" [nzLabel]="pack"></nz-option>
                  }
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          }

          @if (selectedEventType === 'fete-travail') {
            <nz-form-item>
              <nz-form-label>Packs</nz-form-label>
              <nz-form-control>
                <nz-select formControlName="packs" nzMode="multiple" nzPlaceHolder="Sélectionner les packs">
                  @for (pack of packOptions.feteTravail; track pack) {
                    <nz-option [nzValue]="pack" [nzLabel]="pack"></nz-option>
                  }
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          }

          <!-- Interface Drag and Drop -->
          <div class="drag-drop-row">

              <!-- Zone gauche : Services disponibles -->
              <div class="available-services-zone">
                <div class="zone-header">
                  <h5>Services disponibles</h5>
                </div>

                <!-- Barre de recherche -->
                <div class="search-bar">
                  <input
                    nz-input
                    [(ngModel)]="searchTerm"
                    [ngModelOptions]="{standalone: true}"
                    (input)="onSearchChange()"
                    (keyup)="onSearchChange()"
                    placeholder="Rechercher un service..."
                    class="search-input" />
                </div>

                <!-- Liste des services disponibles -->
                <div
                  class="services-list available-list"
                  cdkDropList
                  #availableList="cdkDropList"
                  [cdkDropListData]="filteredServices"
                  [cdkDropListConnectedTo]="[selectedList]"
                  (cdkDropListDropped)="drop($event)">

                  @for (service of filteredServices; track service.id) {
                    <div
                      class="service-item available-item"
                      cdkDrag>
                      <span class="service-label">{{ service.label }}</span>
                      <span class="service-category">{{ service.category }}</span>
                      <div class="service-actions">
                        @if (service.id.startsWith('custom-')) {
                          <button
                            nz-button
                            nzType="text"
                            nzSize="small"
                            (click)="removeAvailableService(service, $event)"
                            class="remove-available-btn">
                            <nz-icon nzType="close"></nz-icon>
                          </button>
                        }
                        <nz-icon nzType="drag" class="drag-handle"></nz-icon>
                      </div>
                    </div>
                  }

                  @if (filteredServices.length === 0) {
                    <div class="empty-list">
                      <nz-icon nzType="inbox" class="empty-icon"></nz-icon>
                      <span class="empty-text">Aucun service trouvé</span>
                    </div>
                  }
                </div>

                <!-- Barre d'ajout -->
                <div class="add-service-bar">
                  <div class="add-service-input-group">
                    <input
                      nz-input
                      [(ngModel)]="newServiceInput"
                      [ngModelOptions]="{standalone: true}"
                      placeholder="Ajouter un service personnalisé..."
                      (keyup.enter)="addNewService()"
                      class="add-input" />
                    <button
                      nz-button
                      nzType="primary"
                      nzSize="small"
                      [disabled]="!newServiceInput || newServiceInput.trim().length === 0"
                      (click)="addNewService()"
                      class="add-btn">
                      <nz-icon nzType="plus"></nz-icon>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Zone droite : Services sélectionnés -->
              <div class="selected-services-zone">
                <div class="zone-header">
                  <h5>Services sélectionnés</h5>
                  <span class="count-badge">{{ selectedServices.length }}</span>
                </div>

                <!-- Liste des services sélectionnés -->
                <div
                  class="services-list selected-list"
                  cdkDropList
                  #selectedList="cdkDropList"
                  [cdkDropListData]="selectedServices"
                  [cdkDropListConnectedTo]="[availableList]"
                  (cdkDropListDropped)="drop($event)">

                  @for (service of selectedServices; track service.id) {
                    <div
                      class="service-item selected-item"
                      cdkDrag>
                      <span class="service-label">{{ service.label }}</span>
                      <button
                        nz-button
                        nzType="text"
                        nzSize="small"
                        (click)="removeSelectedService(service)"
                        class="remove-btn">
                        <nz-icon nzType="close"></nz-icon>
                      </button>
                    </div>
                  }

                  @if (selectedServices.length === 0) {
                    <div class="empty-drop-zone">
                      <nz-icon nzType="drag" class="drop-icon"></nz-icon>
                      <span class="drop-text">Glissez les services ici</span>
                      <span class="drop-hint">Ou double-cliquez sur un service</span>
                    </div>
                  }
                </div>
              </div>
            </div>

          <!-- Thème -->
          <nz-form-item class="theme-section">
            <nz-form-label>Thème</nz-form-label>
            <nz-form-control>
              <input nz-input formControlName="theme" placeholder="Thème de l'événement" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </form>
      </div>
    }

    <!-- Étape 4: Paiement -->
    @if (currentStep() === 3) {
      <div class="step-content">
        <h3>Informations de paiement</h3>

        <form [formGroup]="step4Form" nz-form nzLayout="vertical">
          <div class="form-row">
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>Statut du paiement</nz-form-label>
                <nz-form-control nzErrorTip="Le statut est requis">
                  <nz-select formControlName="statut" nzPlaceHolder="Statut du paiement">
                    <nz-option nzValue="non-paye" nzLabel="Non payé"></nz-option>
                    <nz-option nzValue="paye" nzLabel="Payé"></nz-option>
                    <nz-option nzValue="partiellement-paye" nzLabel="Partiellement payé"></nz-option>
                    <nz-option nzValue="en-attente" nzLabel="En attente"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div class="form-col">
              <nz-form-item>
                <nz-form-label nzRequired>Montant total (DH)</nz-form-label>
                <nz-form-control nzErrorTip="Le montant total est requis">
                  <nz-input-number
                    formControlName="montantTotal"
                    [nzMin]="0"
                    [nzStep]="100"
                    nzPlaceHolder="Montant total">
                  </nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          @if (step4Form.get('statut')?.value === 'partiellement-paye') {
            <div class="form-row">
              <div class="form-col">
                <nz-form-item>
                  <nz-form-label nzRequired>Montant payé (DH)</nz-form-label>
                  <nz-form-control nzErrorTip="Le montant payé est requis">
                    <nz-input-number
                      formControlName="montantPaye"
                      [nzMin]="0"
                      [nzStep]="100"
                      nzPlaceHolder="Montant déjà payé">
                    </nz-input-number>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div class="form-col">
                <nz-form-item>
                  <nz-form-label>Date d'échéance</nz-form-label>
                  <nz-form-control>
                    <nz-date-picker formControlName="dateEcheance" nzPlaceHolder="Date limite de paiement"></nz-date-picker>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          }

          @if (step4Form.get('statut')?.value !== 'non-paye') {
            <nz-form-item>
              <nz-form-label>Méthode de paiement</nz-form-label>
              <nz-form-control>
                <nz-select formControlName="methodePaiement" nzPlaceHolder="Comment le client a-t-il payé ?">
                  <nz-option nzValue="especes" nzLabel="Espèces"></nz-option>
                  <nz-option nzValue="cheque" nzLabel="Chèque"></nz-option>
                  <nz-option nzValue="virement" nzLabel="Virement bancaire"></nz-option>
                  <nz-option nzValue="carte" nzLabel="Carte bancaire"></nz-option>
                  <nz-option nzValue="mobile" nzLabel="Paiement mobile"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          }

        <nz-form-item>
          <nz-form-label>Commentaire sur le paiement</nz-form-label>
          <nz-form-control>
            <textarea
              nz-input
              formControlName="commentairePaiement"
              placeholder="Notes sur les conditions de paiement, accords spéciaux, etc."
              rows="3">
            </textarea>
          </nz-form-control>
        </nz-form-item>

          <!-- Résumé financier -->
          <nz-card nzTitle="Résumé financier" nzSize="small" class="financial-summary">
            <div class="summary-row">
              <span>Montant total:</span>
              <strong>{{ step4Form.get('montantTotal')?.value || 0 }} DH</strong>
            </div>
            @if (step4Form.get('statut')?.value === 'partiellement-paye') {
              <div class="summary-row">
                <span>Montant payé:</span>
                <strong class="paid-amount">{{ step4Form.get('montantPaye')?.value || 0 }} DH</strong>
              </div>
              <div class="summary-row">
                <span>Reste à payer:</span>
                <strong class="remaining-amount">
                  {{ (step4Form.get('montantTotal')?.value || 0) - (step4Form.get('montantPaye')?.value || 0) }} DH
                </strong>
              </div>
            }
          </nz-card>
        </form>
      </div>
    }

    <!-- Étape 5: Source et commentaires -->
    @if (currentStep() === 4) {
      <div class="step-content">
        <h3>Finalisation</h3>

        <form [formGroup]="step5Form" nz-form nzLayout="vertical">
          <nz-form-item>
            <nz-form-label>Source de trafic</nz-form-label>
            <nz-form-control>
              <nz-select formControlName="sourceTraffic" nzPlaceHolder="Comment le client nous a-t-il trouvé ? (optionnel)" nzAllowClear>
                <nz-option nzValue="bouche-a-oreille" nzLabel="Bouche à oreille"></nz-option>
                <nz-option nzValue="facebook" nzLabel="Facebook"></nz-option>
                <nz-option nzValue="instagram" nzLabel="Instagram"></nz-option>
                <nz-option nzValue="google" nzLabel="Recherche Google"></nz-option>
                <nz-option nzValue="site-web" nzLabel="Site web"></nz-option>
                <nz-option nzValue="flyer" nzLabel="Flyer/Prospectus"></nz-option>
                <nz-option nzValue="partenaire" nzLabel="Partenaire"></nz-option>
                <nz-option nzValue="ancien-client" nzLabel="Ancien client"></nz-option>
                <nz-option nzValue="autre" nzLabel="Autre"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-label>Commentaires généraux</nz-form-label>
            <nz-form-control>
              <textarea
                nz-input
                formControlName="commentaires"
                placeholder="Notes supplémentaires, demandes spéciales, observations..."
                rows="4">
              </textarea>
            </nz-form-control>
          </nz-form-item>

          <!-- Récapitulatif de l'événement -->
          <nz-card nzTitle="Récapitulatif de l'événement" nzSize="small" class="event-summary">
            <div class="summary-section">
              <h5>Type d'événement</h5>
              <p>{{ step1Form.get('category')?.value | titlecase }} - {{ step1Form.get('eventType')?.value }}</p>
            </div>

            @if (step2Form.get('clientType')?.value === 'existing') {
              <div class="summary-section">
                <h5>Client</h5>
                <p>{{ getSelectedClientName() }}</p>
              </div>
            }

            @if (step2Form.get('clientType')?.value === 'new') {
              <div class="summary-section">
                <h5>Nouveau client</h5>
                <p>{{ step2Form.get('newClient.nom')?.value }} {{ step2Form.get('newClient.prenom')?.value }}</p>
              </div>
            }

            <div class="summary-section">
              <h5>Détails</h5>
              <p>{{ step3Form.get('nbPersonnes')?.value }} personnes - {{ step3Form.get('ville')?.value }}</p>
              <p>Date: {{ step3Form.get('date')?.value | date:'dd/MM/yyyy' }}</p>
            </div>

            <div class="summary-section">
              <h5>Paiement</h5>
              <p>{{ step4Form.get('montantTotal')?.value }} DH - {{ step4Form.get('statut')?.value }}</p>
            </div>
          </nz-card>
        </form>
      </div>
    }

    <!-- Navigation buttons -->
    <div class="form-actions">
      <button
        nz-button
        nzType="default"
        (click)="prevStep()"
        [disabled]="currentStep() === 0">
        <nz-icon nzType="left"></nz-icon>
        Précédent
      </button>

      @if (currentStep() < 4) {
        <button
          nz-button
          nzType="primary"
          (click)="nextStep()">
          Suivant
          <nz-icon nzType="right"></nz-icon>
        </button>
      }

      @if (currentStep() === 4) {
        <button
          nz-button
          nzType="primary"
          (click)="onSubmit()">
          <nz-icon nzType="check"></nz-icon>
          {{ isEditMode ? 'Modifier l\'événement' : 'Créer l\'événement' }}
        </button>
      }
    </div>
  </nz-card>


</div>
