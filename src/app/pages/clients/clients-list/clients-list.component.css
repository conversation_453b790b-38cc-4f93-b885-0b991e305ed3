/* Container principal */
.clients-container {
  padding: 24px;
  background: var(--apple-gray-50);
  min-height: 100vh;
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--apple-black);
  background: var(--gradient-grenat);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.add-btn {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: 12px !important;
  height: 44px !important;
  padding: 0 24px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(139, 21, 56, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.add-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(139, 21, 56, 0.4) !important;
}

/* Filtres */
.filters-card {
  margin-bottom: 24px;
  border-radius: 16px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid var(--apple-gray-200) !important;
}

:host ::ng-deep .filters-card .ant-card-head {
  border-bottom: 1px solid var(--apple-gray-200) !important;
  background: var(--apple-gray-50) !important;
}

:host ::ng-deep .filters-card .ant-card-head-title {
  color: var(--apple-grenat) !important;
  font-weight: 600 !important;
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr auto;
  gap: 24px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--apple-gray-700);
}

.search-input,
.filter-select {
  height: 44px !important;
  border-radius: 12px !important;
  border: 2px solid var(--apple-gray-200) !important;
  transition: all 0.3s ease !important;
}

.search-input:focus,
:host ::ng-deep .filter-select.ant-select-focused .ant-select-selector {
  border-color: var(--apple-grenat) !important;
  box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1) !important;
}

:host ::ng-deep .filter-select .ant-select-selector {
  height: 44px !important;
  border-radius: 12px !important;
  border: 2px solid var(--apple-gray-200) !important;
}

.clear-btn {
  height: 44px !important;
  border-radius: 12px !important;
  border: 2px solid var(--apple-gray-300) !important;
  color: var(--apple-gray-600) !important;
  transition: all 0.2s ease !important;
}

.clear-btn:hover {
  border-color: var(--apple-grenat) !important;
  color: var(--apple-grenat) !important;
  background: var(--apple-grenat-soft) !important;
}

/* Tableau */
.table-card {
  border-radius: 16px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid var(--apple-gray-200) !important;
}

:host ::ng-deep .clients-table .ant-table-thead > tr > th {
  background: var(--apple-gray-50) !important;
  border-bottom: 2px solid var(--apple-gray-200) !important;
  color: var(--apple-grenat) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  padding: 16px 12px !important;
}

:host ::ng-deep .clients-table .ant-table-tbody > tr > td {
  padding: 16px 12px !important;
  border-bottom: 1px solid var(--apple-gray-100) !important;
  vertical-align: middle !important;
}

:host ::ng-deep .clients-table .ant-table-tbody > tr:hover > td {
  background: var(--apple-grenat-soft) !important;
}

/* Éléments du tableau */
.client-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--gradient-grenat);
  color: var(--apple-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin: 0 auto;
}

.client-info {
  text-align: center;
}

.client-name {
  font-weight: 600;
  color: var(--apple-black);
  margin-bottom: 4px;
}

.client-address {
  font-size: 12px;
  color: var(--apple-gray-600);
}

.cin-badge {
  background: var(--apple-gray-100);
  color: var(--apple-gray-700);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  font-family: monospace;
}

.phone-info {
  font-size: 13px;
  color: var(--apple-gray-700);
  line-height: 1.4;
}

.email-link {
  color: var(--apple-grenat) !important;
  text-decoration: none;
  font-size: 13px;
  transition: all 0.2s ease;
}

.email-link:hover {
  color: var(--apple-grenat-dark) !important;
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.edit-btn {
  background: var(--apple-grenat) !important;
  border-color: var(--apple-grenat) !important;
  border-radius: 8px !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

.edit-btn:hover {
  background: var(--apple-grenat-dark) !important;
  border-color: var(--apple-grenat-dark) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3) !important;
}

/* État vide */
.empty-state {
  text-align: center !important;
  padding: 48px 24px !important;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.empty-icon {
  font-size: 48px;
  color: var(--apple-gray-400);
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--apple-gray-600);
  margin: 0;
}

.empty-hint {
  font-size: 14px;
  color: var(--apple-gray-500);
  margin: 0;
}

/* Pagination */
:host ::ng-deep .ant-pagination {
  margin-top: 24px !important;
  text-align: center !important;
}

:host ::ng-deep .ant-pagination .ant-pagination-item {
  border-radius: 8px !important;
  border: 1px solid var(--apple-gray-200) !important;
}

:host ::ng-deep .ant-pagination .ant-pagination-item-active {
  background: var(--apple-grenat) !important;
  border-color: var(--apple-grenat) !important;
}

:host ::ng-deep .ant-pagination .ant-pagination-item-active a {
  color: var(--apple-white) !important;
}

/* Responsive */
@media (max-width: 768px) {
  .filters-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
