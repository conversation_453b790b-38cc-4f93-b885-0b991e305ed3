<div class="clients-container">
  <!-- Header avec titre et bouton d'ajout -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">Liste des clients</h2>
      <button nz-button nzType="primary" routerLink="/clients/new" class="add-btn">
        <nz-icon nzType="plus"></nz-icon>
        Nouveau client
      </button>
    </div>
  </div>

  <!-- Filtres -->
  <nz-card class="filters-card" nzTitle="Filtres">
    <div class="filters-row">
      <div class="filter-group">
        <label class="filter-label">Recherche</label>
        <input 
          nz-input 
          [(ngModel)]="searchTerm" 
          (ngModelChange)="onSearchChange()"
          placeholder="Nom, prénom, email, CIN, téléphone..." 
          class="search-input" />
      </div>
      
      <div class="filter-group">
        <label class="filter-label">Ville</label>
        <nz-select 
          [(ngModel)]="selectedVille" 
          (ngModelChange)="onVilleChange()"
          nzPlaceHolder="Toutes les villes"
          nzAllowClear
          class="filter-select">
          @for (ville of villes; track ville) {
            <nz-option [nzValue]="ville" [nzLabel]="ville"></nz-option>
          }
        </nz-select>
      </div>

      <div class="filter-actions">
        <button nz-button nzType="default" (click)="clearFilters()" class="clear-btn">
          <nz-icon nzType="clear"></nz-icon>
          Effacer
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Tableau des clients -->
  <nz-card class="table-card">
    <nz-table 
      #clientsTable 
      [nzData]="filteredClients" 
      [nzLoading]="loading"
      [nzPageSize]="pageSize"
      [nzPageIndex]="pageIndex"
      [nzTotal]="total"
      [nzShowSizeChanger]="true"
      [nzPageSizeOptions]="[10, 20, 50]"
      (nzPageIndexChange)="onPageIndexChange($event)"
      (nzPageSizeChange)="onPageSizeChange($event)"
      nzShowPagination
      nzSize="middle"
      class="clients-table">
      
      <thead>
        <tr>
          <th nzAlign="center">
            <nz-icon nzType="user"></nz-icon>
          </th>
          <th nzAlign="center">Client</th>
          <th nzAlign="center">CIN</th>
          <th nzAlign="center">Ville</th>
          <th nzAlign="center">Téléphone</th>
          <th nzAlign="center">Email</th>
          <th nzAlign="center">Événements</th>
          <th nzAlign="center">Actions</th>
        </tr>
      </thead>
      
      <tbody>
        @for (client of clientsTable.data; track client.id) {
          <tr>
            <td nzAlign="center">
              <div class="client-avatar">
                {{ client.prenom.charAt(0) }}{{ client.nom.charAt(0) }}
              </div>
            </td>
            <td nzAlign="center">
              <div class="client-info">
                <div class="client-name">{{ getClientFullName(client) }}</div>
                <div class="client-address">{{ client.adresse }}</div>
              </div>
            </td>
            <td nzAlign="center">
              <span class="cin-badge">{{ client.cin }}</span>
            </td>
            <td nzAlign="center">
              <nz-tag [nzColor]="'blue'">{{ client.ville }}</nz-tag>
            </td>
            <td nzAlign="center">
              <div class="phone-info">
                {{ getClientPhone(client) }}
              </div>
            </td>
            <td nzAlign="center">
              <a [href]="'mailto:' + client.email" class="email-link">{{ client.email }}</a>
            </td>
            <td nzAlign="center">
              <nz-tag [nzColor]="client.nombreEvenements > 0 ? 'green' : 'default'">
                {{ client.nombreEvenements }} événement{{ client.nombreEvenements > 1 ? 's' : '' }}
              </nz-tag>
            </td>
            <td nzAlign="center">
              <div class="action-buttons">
                <button 
                  nz-button 
                  nzType="primary" 
                  nzSize="small"
                  [routerLink]="['/clients/edit', client.id]"
                  class="edit-btn">
                  <nz-icon nzType="edit"></nz-icon>
                </button>
              </div>
            </td>
          </tr>
        } @empty {
          <tr>
            <td colspan="8" class="empty-state">
              <div class="empty-content">
                <nz-icon nzType="inbox" class="empty-icon"></nz-icon>
                <p class="empty-text">Aucun client trouvé</p>
                <p class="empty-hint">Essayez de modifier vos critères de recherche</p>
              </div>
            </td>
          </tr>
        }
      </tbody>
    </nz-table>
  </nz-card>
</div>
