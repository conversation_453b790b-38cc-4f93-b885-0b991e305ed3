<div class="services-config-container">
  <!-- Header avec titre -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">Configuration des services - {{ categoryTitle }}</h2>
      <div class="header-actions">
        <button
          nz-button
          nzType="default"
          (click)="resetConfiguration()"
          [nzLoading]="isLoading">
          <nz-icon nzType="reload"></nz-icon>
          Réinitialiser
        </button>
        <button
          nz-button
          nzType="primary"
          (click)="saveConfiguration()"
          [nzLoading]="isLoading"
          class="save-btn">
          <nz-icon nzType="save"></nz-icon>
          Sauvegarder
        </button>
      </div>
    </div>
  </div>

  <nz-card class="config-card">
    <div class="config-description">
      <p>
        Configurez quels services seront disponibles pour chaque type d'événement dans la catégorie
        <strong>{{ categoryTitle }}</strong>.
        Activez ou désactivez les services en utilisant les interrupteurs dans le tableau ci-dessous.
      </p>
    </div>

    <!-- Bouton d'ajout de service -->
    <div class="add-service-section">
      <button
        nz-button
        nzType="dashed"
        (click)="addNewService()"
        class="add-service-btn">
        <nz-icon nzType="plus"></nz-icon>
        Ajouter un service
      </button>
    </div>

    <!-- Tableau matriciel avec colonnes fixes -->
    <div class="matrix-table-wrapper">
      <div class="matrix-table">
        <table class="services-matrix">
          <thead>
            <tr>
              <th class="service-header fixed-left">Services</th>
              @for (eventType of eventTypes; track eventType) {
                <th class="event-type-header">{{ eventType }}</th>
              }
              <th class="actions-header fixed-right">Actions</th>
            </tr>
          </thead>
          <tbody>
            @for (service of services; track service.serviceId; let i = $index) {
              <tr>
                <td class="service-name fixed-left">{{ service.serviceName }}</td>
                @for (eventType of eventTypes; track eventType) {
                  <td class="switch-cell">
                    <nz-switch
                      [ngModel]="service[eventType]"
                      (ngModelChange)="onServiceToggle(service, eventType, $event)"
                      nzSize="small">
                    </nz-switch>
                  </td>
                }
                <td class="actions-cell fixed-right">
                  <button
                    nz-button
                    nzType="text"
                    nzDanger
                    nzSize="small"
                    (click)="removeService(i)"
                    [disabled]="services.length <= 1"
                    nz-tooltip="Supprimer ce service"
                    class="delete-service-btn">
                    <nz-icon nzType="delete"></nz-icon>
                  </button>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>

    <!-- Résumé -->
    <div class="config-summary">
      <div class="summary-stats">
        <div class="stat-item">
          <span class="stat-label">Services configurés :</span>
          <span class="stat-value">{{ services.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Types d'événements :</span>
          <span class="stat-value">{{ eventTypes.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Configurations actives :</span>
          <span class="stat-value">{{ activeConfigurationsCount }}</span>
        </div>
      </div>
    </div>

  </nz-card>
</div>
