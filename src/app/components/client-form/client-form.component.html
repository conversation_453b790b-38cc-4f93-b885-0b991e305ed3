<div class="client-form-container">
  <nz-card [nzTitle]="isEditMode ? 'Modifier le client' : 'Nouveau client'" class="client-form-card">
    <form [formGroup]="clientForm" (ngSubmit)="onSubmit()" nz-form nzLayout="vertical">
    
    <!-- Nom et Prénom -->
    <div nz-row [nzGutter]="16">
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>Nom</nz-form-label>
          <nz-form-control [nzErrorTip]="nomError || ''">
            <input 
              nz-input 
              formControlName="nom" 
              placeholder="Nom de famille"
              [class.error]="nomError" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>Prénom</nz-form-label>
          <nz-form-control [nzErrorTip]="prenomError || ''">
            <input
              nz-input
              formControlName="prenom"
              placeholder="Prénom"
              [class.error]="prenomError" />
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- CIN et Ville -->
    <div nz-row [nzGutter]="16">
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>CIN</nz-form-label>
          <nz-form-control [nzErrorTip]="cinError || ''">
            <input
              nz-input
              formControlName="cin"
              placeholder="Ex: AB123456"
              [class.error]="cinError"
              style="text-transform: uppercase;" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>Ville</nz-form-label>
          <nz-form-control [nzErrorTip]="villeError || ''">
            <input
              nz-input
              formControlName="ville"
              placeholder="Ville de résidence"
              [class.error]="villeError" />
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- Adresse -->
    <nz-form-item>
      <nz-form-label nzRequired>Adresse</nz-form-label>
      <nz-form-control [nzErrorTip]="adresseError || ''">
        <textarea
          nz-input
          formControlName="adresse"
          placeholder="Adresse complète"
          rows="3"
          [class.error]="adresseError">
        </textarea>
      </nz-form-control>
    </nz-form-item>

    <!-- Téléphones -->
    <div nz-row [nzGutter]="16">
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label nzRequired>Téléphone principal</nz-form-label>
          <nz-form-control [nzErrorTip]="telephone1Error || ''">
            <input
              nz-input
              formControlName="telephone1"
              placeholder="0612345678"
              [class.error]="telephone1Error"
              maxlength="10" />
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="12">
        <nz-form-item>
          <nz-form-label>Téléphone secondaire</nz-form-label>
          <nz-form-control [nzErrorTip]="telephone2Error || ''">
            <input
              nz-input
              formControlName="telephone2"
              placeholder="0522123456 (optionnel)"
              [class.error]="telephone2Error"
              maxlength="10" />
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- Email -->
    <nz-form-item>
      <nz-form-label nzRequired>Email</nz-form-label>
      <nz-form-control [nzErrorTip]="emailError || ''">
        <input 
          nz-input 
          formControlName="email" 
          placeholder="<EMAIL>"
          type="email"
          [class.error]="emailError" />
      </nz-form-control>
    </nz-form-item>

    <!-- Actions -->
    <div class="form-actions">
      <button 
        nz-button 
        nzType="default" 
        type="button"
        [disabled]="isSubmitting">
        Annuler
      </button>
      
      <button 
        nz-button 
        nzType="primary" 
        type="submit"
        [nzLoading]="isSubmitting"
        [disabled]="!clientForm.valid">
        <nz-icon nzType="save" *ngIf="!isSubmitting"></nz-icon>
        {{ isSubmitting ? 'Création...' : 'Créer le client' }}
      </button>
    </div>

    <!-- Aide -->
    <div class="form-help">
      <h4>Aide pour le remplissage :</h4>
      <ul>
        <li><strong>CIN :</strong> Format attendu : 1-2 lettres suivies de 6 chiffres (ex: AB123456)</li>
        <li><strong>Téléphone :</strong> 10 chiffres sans espaces (ex: 0612345678)</li>
        <li><strong>Email :</strong> Adresse email valide pour les communications</li>
      </ul>
    </div>
    </form>
  </nz-card>
</div>
