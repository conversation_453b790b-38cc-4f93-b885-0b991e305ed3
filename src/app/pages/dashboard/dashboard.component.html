<div class="dashboard-container">
  <!-- Header avec titre et actions rapides -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="welcome-section">
        <h1 class="dashboard-title">Tableau de bord</h1>
        <p class="dashboard-subtitle">Bienvenue dans votre espace de gestion Traiteria</p>
      </div>
      <div class="header-actions">
        <button 
          nz-button 
          nzType="default" 
          (click)="viewStatistics()"
          class="action-btn">
          <nz-icon nzType="bar-chart"></nz-icon>
          Statistiques
        </button>
        <button 
          nz-button 
          nzType="default" 
          (click)="viewReports()"
          class="action-btn">
          <nz-icon nzType="file-text"></nz-icon>
          Rapports
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques principales -->
  <div class="stats-grid">
    <nz-card class="stat-card revenue-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon revenue-icon">💰</div>
        <div class="stat-info">
          <nz-statistic
            [nzValue]="formatCurrency(stats.monthlyRevenue)"
            nzTitle="Chiffre d'affaires mensuel"
            class="stat-number">
          </nz-statistic>
          <div class="stat-trend positive">
            <nz-icon nzType="arrow-up"></nz-icon>
            +12% ce mois
          </div>
        </div>
      </div>
    </nz-card>

    <nz-card class="stat-card events-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon events-icon">🎉</div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="stats.totalEvents" 
            nzTitle="Événements ce mois"
            class="stat-number">
          </nz-statistic>
          <div class="stat-trend positive">
            <nz-icon nzType="arrow-up"></nz-icon>
            +8 nouveaux
          </div>
        </div>
      </div>
    </nz-card>

    <nz-card class="stat-card clients-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon clients-icon">👥</div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="stats.activeClients" 
            nzTitle="Clients actifs"
            class="stat-number">
          </nz-statistic>
          <div class="stat-trend positive">
            <nz-icon nzType="arrow-up"></nz-icon>
            +3 cette semaine
          </div>
        </div>
      </div>
    </nz-card>

    <nz-card class="stat-card completion-card" [nzLoading]="isLoading">
      <div class="stat-content">
        <div class="stat-icon completion-icon">✅</div>
        <div class="stat-info">
          <nz-statistic 
            [nzValue]="stats.completionRate" 
            nzSuffix="%" 
            nzTitle="Taux de réussite"
            class="stat-number">
          </nz-statistic>
          <nz-progress 
            [nzPercent]="stats.completionRate" 
            nzSize="small" 
            nzStrokeColor="#34c759"
            class="completion-progress">
          </nz-progress>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Contenu principal -->
  <div class="main-content">
    <!-- Actions rapides -->
    <nz-card class="quick-actions-card">
      <div class="card-header">
        <h3 class="card-title">Actions rapides</h3>
        <p class="card-subtitle">Accédez rapidement aux fonctionnalités principales</p>
      </div>
      <div class="quick-actions-grid">
        @for (action of quickActions; track action.route) {
          <div 
            class="quick-action-item" 
            (click)="navigateToAction(action.route)"
            [style.border-left-color]="action.color">
            <div class="action-icon" [style.color]="action.color">
              <nz-icon [nzType]="action.icon"></nz-icon>
            </div>
            <div class="action-content">
              <h4 class="action-title">{{ action.title }}</h4>
              <p class="action-description">{{ action.description }}</p>
            </div>
            <div class="action-arrow">
              <nz-icon nzType="arrow-right"></nz-icon>
            </div>
          </div>
        }
      </div>
    </nz-card>

    <!-- Événements récents -->
    <nz-card class="recent-events-card">
      <div class="card-header">
        <div class="header-left">
          <h3 class="card-title">Événements récents</h3>
          <p class="card-subtitle">Derniers événements créés ou modifiés</p>
        </div>
        <button 
          nz-button 
          nzType="link" 
          (click)="viewAllEvents()"
          class="view-all-btn">
          Voir tout
          <nz-icon nzType="arrow-right"></nz-icon>
        </button>
      </div>
      
      <div class="events-list">
        @for (event of recentEvents; track event.id) {
          <div class="event-item">
            <div class="event-avatar">
              <nz-avatar [nzText]="getClientInitials(event.client)" nzSize="large"></nz-avatar>
            </div>
            <div class="event-info">
              <h4 class="event-title">{{ event.title }}</h4>
              <p class="event-client">{{ event.client }}</p>
              <div class="event-meta">
                <span class="event-date">{{ formatDate(event.date) }}</span>
                <span class="event-type">{{ event.type }}</span>
              </div>
            </div>
            <div class="event-status">
              <nz-tag [nzColor]="getStatusColor(event.status)">
                {{ getStatusText(event.status) }}
              </nz-tag>
            </div>
            <div class="event-revenue">
              <span class="revenue-amount">{{ formatCurrency(event.revenue) }}</span>
            </div>
          </div>
        }
      </div>
    </nz-card>
  </div>
</div>
