import { Component, OnInit } from '@angular/core';
import {RouterLink, RouterLinkActive, RouterOutlet, Router, NavigationEnd, ActivatedRoute} from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { filter } from 'rxjs/operators';

interface BreadcrumbItem {
  label: string;
  url?: string;
  icon?: string;
}

@Component({
  selector: 'app-root',
  imports: [RouterLink, RouterOutlet, NzIconModule, NzLayoutModule, NzMenuModule, RouterLinkActive, NzBreadCrumbModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  isCollapsed = false;
  breadcrumbs: BreadcrumbItem[] = [];

  constructor(private router: Router) {}

  ngOnInit() {
    // Écouter les changements de route pour mettre à jour le breadcrumb
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateBreadcrumbs();
      });

    // Initialiser le breadcrumb
    this.updateBreadcrumbs();
  }

  isEventsListActive(): boolean {
    const url = this.router.url;
    return url === '/events' || url.startsWith('/events/edit/');
  }

  private updateBreadcrumbs() {
    const url = this.router.url;
    this.breadcrumbs = [];

    // Toujours commencer par Accueil
    this.breadcrumbs.push({ label: 'Accueil', url: '/welcome', icon: 'home' });

    if (url.startsWith('/events')) {
      this.breadcrumbs.push({ label: 'Événements', url: '/events', icon: 'calendar' });

      if (url === '/events/new') {
        this.breadcrumbs.push({ label: 'Nouvel événement' });
      } else if (url.startsWith('/events/edit/')) {
        this.breadcrumbs.push({ label: 'Modifier événement' });
      }
    } else if (url.startsWith('/clients')) {
      this.breadcrumbs.push({ label: 'Clients', url: '/clients', icon: 'user' });

      if (url === '/clients/new') {
        this.breadcrumbs.push({ label: 'Nouveau client' });
      }
    } else if (url === '/welcome') {
      // Déjà ajouté Accueil
    } else {
      // Page inconnue, garder juste Accueil
    }
  }
}
