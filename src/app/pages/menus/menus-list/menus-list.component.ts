import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';

// Ng-Zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzMenuModule } from 'ng-zorro-antd/menu';

interface MenuItem {
  id: string;
  name: string;
  checked: boolean;
}

interface Menu {
  id: string;
  title: string;
  description?: string;
  items: MenuItem[];
  price?: number;
  category: string;
  isActive: boolean;
}

@Component({
  selector: 'app-menus-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    NzCardModule,
    NzButtonModule,
    NzInputModule,
    NzIconModule,
    NzTagModule,
    NzGridModule,
    NzToolTipModule,
    NzPopconfirmModule,
    NzDropDownModule,
    NzMenuModule
  ],
  templateUrl: './menus-list.component.html',
  styleUrl: './menus-list.component.css'
})
export class MenusListComponent implements OnInit {
  menus: Menu[] = [];
  filteredMenus: Menu[] = [];
  searchTerm = '';

  ngOnInit(): void {
    this.loadMenus();
  }

  loadMenus(): void {
    // Simulation de données
    this.menus = [
      {
        id: '1',
        title: 'Menu Sultana',
        description: 'Menu premium pour événements de prestige',
        category: 'Premium',
        isActive: true,
        price: 450,
        items: [
          { id: '1', name: 'Cocktail de bienvenue', checked: true },
          { id: '2', name: 'Amuse bouche: Macaron, amande, nougat et fekkas', checked: true },
          { id: '3', name: 'Jus avec gâteaux soirée', checked: true },
          { id: '4', name: 'Sushi', checked: true },
          { id: '5', name: 'Briouate et mini cigare au fromage', checked: true },
          { id: '6', name: 'Pastilla de fruits de mer', checked: true },
          { id: '7', name: 'Mechoui d\'agneau garni', checked: true },
          { id: '8', name: 'Fruits de saison', checked: true },
          { id: '9', name: 'Soda et eau minérale', checked: true },
          { id: '10', name: 'Thé avec des gâteaux Marocains', checked: true },
          { id: '11', name: 'Tarte glacée', checked: true },
          { id: '12', name: 'Centre de table', checked: true }
        ]
      },
      {
        id: '2',
        title: 'Menu Royal',
        description: 'Menu traditionnel marocain authentique',
        category: 'Traditionnel',
        isActive: true,
        price: 350,
        items: [
          { id: '1', name: 'Thé à la menthe', checked: true },
          { id: '2', name: 'Chebakia et gâteaux traditionnels', checked: true },
          { id: '3', name: 'Harira traditionnelle', checked: true },
          { id: '4', name: 'Tajine d\'agneau aux pruneaux', checked: true },
          { id: '5', name: 'Couscous royal', checked: true },
          { id: '6', name: 'Pastilla au pigeon', checked: true },
          { id: '7', name: 'Salade marocaine', checked: true },
          { id: '8', name: 'Fruits et pâtisseries', checked: true }
        ]
      },
      {
        id: '3',
        title: 'Menu Business',
        description: 'Menu adapté aux événements d\'entreprise',
        category: 'Entreprise',
        isActive: true,
        price: 280,
        items: [
          { id: '1', name: 'Café et viennoiseries', checked: true },
          { id: '2', name: 'Pause café avec petits fours', checked: true },
          { id: '3', name: 'Lunch box premium', checked: true },
          { id: '4', name: 'Sandwichs variés', checked: true },
          { id: '5', name: 'Salade composée', checked: true },
          { id: '6', name: 'Boissons fraîches', checked: true },
          { id: '7', name: 'Dessert du jour', checked: true }
        ]
      }
    ];
    
    this.applyFilters();
  }

  applyFilters(): void {
    let filtered = [...this.menus];

    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(menu => 
        menu.title.toLowerCase().includes(searchLower) ||
        menu.description?.toLowerCase().includes(searchLower) ||
        menu.category.toLowerCase().includes(searchLower)
      );
    }

    this.filteredMenus = filtered;
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.applyFilters();
  }
}
