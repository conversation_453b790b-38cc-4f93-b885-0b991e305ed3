import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Ng-Zorro imports
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';

interface NewMaterial {
  name: string;
  description: string;
  quantity: number;
  unit: string;
}

@Component({
  selector: 'app-add-material-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzModalModule,
    NzInputModule,
    NzButtonModule,
    NzFormModule
  ],
  template: `
    <div class="add-material-form">
      <div class="form-group">
        <label>Nom du matériel :</label>
        <input 
          nz-input 
          [(ngModel)]="newMaterial.name"
          placeholder="Ex: Tapis rouge, Podium..."
          class="form-input">
      </div>
      
      <div class="form-group">
        <label>Description :</label>
        <input 
          nz-input 
          [(ngModel)]="newMaterial.description"
          placeholder="Description du matériel"
          class="form-input">
      </div>
      
      <div class="form-group">
        <label>Quantité initiale :</label>
        <input 
          nz-input 
          type="number"
          [(ngModel)]="newMaterial.quantity"
          placeholder="0"
          class="form-input">
      </div>
      
      <div class="form-group">
        <label>Unité :</label>
        <input 
          nz-input 
          [(ngModel)]="newMaterial.unit"
          placeholder="Ex: unité(s), kit(s), m²..."
          class="form-input">
      </div>
    </div>
  `,
  styles: [`
    .add-material-form {
      padding: 16px 0;
    }
    
    .form-group {
      margin-bottom: 16px;
    }
    
    .form-group:last-child {
      margin-bottom: 0;
    }
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--apple-gray-700);
    }
    
    .form-input {
      width: 100%;
      border-radius: 8px;
      border: 1px solid var(--apple-gray-300);
      transition: all 0.2s ease;
    }
    
    .form-input:focus {
      border-color: var(--apple-grenat);
      box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1);
    }
  `]
})
export class AddMaterialModalComponent {
  @Output() materialAdded = new EventEmitter<NewMaterial>();

  newMaterial: NewMaterial = {
    name: '',
    description: '',
    quantity: 0,
    unit: 'unité(s)'
  };

  onSubmit(): void {
    if (this.newMaterial.name.trim()) {
      this.materialAdded.emit({ ...this.newMaterial });
      this.resetForm();
    }
  }

  resetForm(): void {
    this.newMaterial = {
      name: '',
      description: '',
      quantity: 0,
      unit: 'unité(s)'
    };
  }

  isValid(): boolean {
    return this.newMaterial.name.trim().length > 0;
  }
}
